import { Metadata } from "next"
import SystemMonitoringDashboard from "@/components/monitoring/system-monitoring-dashboard"
import { checkPermission } from "@/lib/auth/permission-check"
import { redirect } from "next/navigation"
import { getServerSession } from "next-auth"
import { authOptions } from "@/auth"

export const metadata: Metadata = {
  title: "系统监控 - 聆花景泰蓝工艺品管理系统",
  description: "实时系统性能监控、安全状态和资源使用情况"
}

export default async function MonitoringPage() {
  const session = await getServerSession(authOptions)
  
  if (!session?.user?.id) {
    redirect("/login")
  }

  // 检查系统管理员权限
  const hasPermission = await checkPermission(session.user.id, "system.monitor")
  
  if (!hasPermission) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center space-y-4">
          <div className="text-6xl">🔒</div>
          <h2 className="text-2xl font-bold">访问受限</h2>
          <p className="text-muted-foreground">
            您没有访问系统监控的权限，请联系管理员
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6">
      <SystemMonitoringDashboard />
    </div>
  )
}