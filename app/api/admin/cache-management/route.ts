import { NextRequest } from "next/server"
import { withEnhancedPermission, PERMISSIONS } from "@/lib/auth/enhanced-auth-middleware"
import { with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, UnifiedErrorHandler, ErrorFactory } from "@/lib/api/unified-error-handler"
import { withRateLimit, RATE_LIMIT_CONFIGS } from "@/lib/api/rate-limiter"
import { 
  getAllCacheStats, 
  clearAllCaches, 
  warmupCache, 
  cacheInstances 
} from "@/lib/cache/cache-manager"

/**
 * 获取缓存统计信息
 */
export const GET = withErrorHandler(async (req: NextRequest) => {
  try {
    // 应用速率限制
    const rateLimitCheck = await withRateLimit(RATE_LIMIT_CONFIGS.STANDARD)(req)
    if (rateLimitCheck) return rateLimitCheck

    // 权限验证 - 需要系统管理员权限
    const permissionCheck = await withEnhancedPermission(req, PERMISSIONS.SYSTEM_ADMIN)
    if (permissionCheck) return permissionCheck

    const stats = getAllCacheStats()
    
    // 计算总体统计
    const totalStats = {
      totalKeys: 0,
      totalHits: 0,
      totalMisses: 0,
      totalMemoryUsage: 0,
      averageHitRate: 0
    }

    let validCaches = 0
    for (const cacheStats of Object.values(stats)) {
      totalStats.totalKeys += cacheStats.totalKeys
      totalStats.totalHits += cacheStats.totalHits
      totalStats.totalMisses += cacheStats.totalMisses
      totalStats.totalMemoryUsage += cacheStats.memoryUsage
      
      if (cacheStats.totalHits + cacheStats.totalMisses > 0) {
        totalStats.averageHitRate += cacheStats.hitRate
        validCaches++
      }
    }

    if (validCaches > 0) {
      totalStats.averageHitRate = totalStats.averageHitRate / validCaches
    }

    return UnifiedErrorHandler.success({
      cacheStats: stats,
      totalStats,
      timestamp: new Date().toISOString()
    }, "缓存统计信息获取成功")

  } catch (error) {
    console.error("获取缓存统计失败:", error)
    throw error instanceof Error ? error : ErrorFactory.internal("获取缓存统计失败")
  }
})

/**
 * 缓存管理操作
 */
export const POST = withErrorHandler(async (req: NextRequest) => {
  try {
    // 应用速率限制
    const rateLimitCheck = await withRateLimit(RATE_LIMIT_CONFIGS.STRICT)(req)
    if (rateLimitCheck) return rateLimitCheck

    // 权限验证 - 需要系统管理员权限
    const permissionCheck = await withEnhancedPermission(req, PERMISSIONS.SYSTEM_ADMIN)
    if (permissionCheck) return permissionCheck

    const { action, cacheType, key } = await req.json()

    if (!action) {
      throw ErrorFactory.validation("操作类型为必填项", "action")
    }

    let result: any = {}

    switch (action) {
      case 'clear_all':
        clearAllCaches()
        result = { message: "所有缓存已清空" }
        break

      case 'clear_cache':
        if (!cacheType) {
          throw ErrorFactory.validation("缓存类型为必填项", "cacheType")
        }
        
        const cache = cacheInstances[cacheType as keyof typeof cacheInstances]
        if (!cache) {
          throw ErrorFactory.validation(`无效的缓存类型: ${cacheType}`, "cacheType")
        }
        
        cache.clear()
        result = { 
          message: `${cacheType} 缓存已清空`,
          cacheType 
        }
        break

      case 'delete_key':
        if (!cacheType || !key) {
          throw ErrorFactory.validation("缓存类型和键为必填项", "cacheType,key")
        }
        
        const targetCache = cacheInstances[cacheType as keyof typeof cacheInstances]
        if (!targetCache) {
          throw ErrorFactory.validation(`无效的缓存类型: ${cacheType}`, "cacheType")
        }
        
        const deleted = targetCache.delete(key)
        result = { 
          message: deleted ? `缓存键 ${key} 已删除` : `缓存键 ${key} 不存在`,
          deleted,
          cacheType,
          key 
        }
        break

      case 'warmup':
        await warmupCache()
        result = { message: "缓存预热完成" }
        break

      case 'get_item':
        if (!cacheType || !key) {
          throw ErrorFactory.validation("缓存类型和键为必填项", "cacheType,key")
        }
        
        const itemCache = cacheInstances[cacheType as keyof typeof cacheInstances]
        if (!itemCache) {
          throw ErrorFactory.validation(`无效的缓存类型: ${cacheType}`, "cacheType")
        }
        
        const item = itemCache.getItem(key)
        result = { 
          item,
          exists: item !== null,
          cacheType,
          key 
        }
        break

      default:
        throw ErrorFactory.validation(`不支持的操作类型: ${action}`, "action")
    }

    return UnifiedErrorHandler.success(result, "缓存操作执行成功")

  } catch (error) {
    console.error("缓存管理操作失败:", error)
    throw error instanceof Error ? error : ErrorFactory.internal("缓存管理操作失败")
  }
})