import { NextRequest } from "next/server"
import { withEnhancedPermission, PERMISSIONS } from "@/lib/auth/enhanced-auth-middleware"
import { withErrorHandler, UnifiedErrorHandler, ErrorFactory } from "@/lib/api/unified-error-handler"
import { withRateLimit, RATE_LIMIT_CONFIGS } from "@/lib/api/rate-limiter"
import { databaseOptimizer } from "@/lib/database/index-optimizer"

/**
 * 获取数据库优化报告
 */
export const GET = withErrorHandler(async (req: NextRequest) => {
  try {
    // 应用速率限制 - 数据库操作使用严格限制
    const rateLimitCheck = await withRateLimit(RATE_LIMIT_CONFIGS.STRICT)(req)
    if (rateLimitCheck) return rateLimitCheck

    // 权限验证 - 需要系统管理员权限
    const permissionCheck = await withEnhancedPermission(req, PERMISSIONS.SYSTEM_ADMIN)
    if (permissionCheck) return permissionCheck

    const report = await databaseOptimizer.generateOptimizationReport()

    return UnifiedErrorHandler.success({
      report,
      timestamp: new Date().toISOString(),
      message: "数据库优化报告生成成功"
    })
  } catch (error) {
    console.error("生成数据库优化报告失败:", error)
    throw error instanceof Error ? error : ErrorFactory.internal("生成数据库优化报告失败")
  }
})

/**
 * 执行数据库索引优化
 */
export const POST = withErrorHandler(async (req: NextRequest) => {
  try {
    // 应用速率限制 - 数据库操作使用严格限制
    const rateLimitCheck = await withRateLimit(RATE_LIMIT_CONFIGS.STRICT)(req)
    if (rateLimitCheck) return rateLimitCheck

    // 权限验证 - 需要系统管理员权限
    const permissionCheck = await withEnhancedPermission(req, PERMISSIONS.SYSTEM_ADMIN)
    if (permissionCheck) return permissionCheck

    const { executeAll = false, selectedIndexes = [] } = await req.json()

    let suggestions = await databaseOptimizer.generateIndexSuggestions()

    // 如果不是执行全部，则只执行选定的索引
    if (!executeAll && selectedIndexes.length > 0) {
      suggestions = suggestions.filter(s => 
        selectedIndexes.includes(`${s.tableName}.${s.columnNames.join('_')}`)
      )
    }

    // 如果没有要执行的索引，返回错误
    if (suggestions.length === 0) {
      throw ErrorFactory.validation("没有要执行的索引优化")
    }

    console.log(`开始执行 ${suggestions.length} 个索引优化...`)

    const result = await databaseOptimizer.executeIndexCreation(suggestions)

    return UnifiedErrorHandler.success({
      result,
      totalSuggestions: suggestions.length,
      createdCount: result.created.length,
      failedCount: result.failed.length,
      timestamp: new Date().toISOString(),
      message: `数据库索引优化完成，成功创建 ${result.created.length} 个索引，失败 ${result.failed.length} 个`
    })
  } catch (error) {
    console.error("执行数据库索引优化失败:", error)
    throw error instanceof Error ? error : ErrorFactory.internal("执行数据库索引优化失败")
  }
})