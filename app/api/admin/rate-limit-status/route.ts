import { NextRequest } from "next/server"
import { withEnhancedPermission, PERMISSIONS } from "@/lib/auth/enhanced-auth-middleware"
import { withErrorHand<PERSON>, UnifiedErrorHandler } from "@/lib/api/unified-error-handler"
import { getRateLimitStats, resetRateLimit } from "@/lib/api/rate-limiter"

/**
 * 获取速率限制状态
 */
export const GET = withErrorHandler(async (req: NextRequest) => {
  try {
    // 权限验证 - 需要系统管理员权限
    const permissionCheck = await withEnhancedPermission(req, PERMISSIONS.SYSTEM_ADMIN)
    if (permissionCheck) return permissionCheck

    const stats = getRateLimitStats()

    return UnifiedErrorHandler.success({
      stats,
      timestamp: new Date().toISOString(),
      message: "速率限制状态获取成功"
    })
  } catch (error) {
    console.error("获取速率限制状态失败:", error)
    throw error
  }
})

/**
 * 重置指定key的速率限制
 */
export const POST = withErrorHandler(async (req: NextRequest) => {
  try {
    // 权限验证 - 需要系统管理员权限
    const permissionCheck = await withEnhancedPermission(req, PERMISSIONS.SYSTEM_ADMIN)
    if (permissionCheck) return permissionCheck

    const { key } = await req.json()
    
    if (!key) {
      throw new Error("key参数为必填项")
    }

    resetRateLimit(key)

    return UnifiedErrorHandler.success({
      key,
      message: `速率限制重置成功: ${key}`
    })
  } catch (error) {
    console.error("重置速率限制失败:", error)
    throw error
  }
})