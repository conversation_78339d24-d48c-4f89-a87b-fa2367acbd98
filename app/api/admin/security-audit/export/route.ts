import { NextRequest } from "next/server"
import { withEnhancedPermission, PERMISSIONS } from "@/lib/auth/enhanced-auth-middleware"
import { with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, UnifiedErrorHandler, ErrorFactory } from "@/lib/api/unified-error-handler"
import { withRateLimit, RATE_LIMIT_CONFIGS } from "@/lib/api/rate-limiter"

/**
 * 导出安全审计日志
 */
export const GET = withErrorHandler(async (req: NextRequest) => {
  try {
    // 应用速率限制
    const rateLimitCheck = await withRateLimit(RATE_LIMIT_CONFIGS.EXPORT)(req)
    if (rateLimitCheck) return rateLimitCheck

    // 权限验证
    const permissionCheck = await withEnhancedPermission(req, PERMISSIONS.SYSTEM_ADMIN)
    if (permissionCheck) return permissionCheck

    const url = new URL(req.url)
    const format = url.searchParams.get('format') || 'csv'
    const search = url.searchParams.get('search') || ''
    const severity = url.searchParams.get('severity') || 'all'
    const eventType = url.searchParams.get('eventType') || 'all'
    const startDate = url.searchParams.get('startDate')
    const endDate = url.searchParams.get('endDate')

    const auditData = await getSecurityAuditDataForExport({
      search,
      severity,
      eventType,
      startDate,
      endDate
    })

    const exportContent = await generateExportContent(auditData, format)
    const mimeType = getExportMimeType(format)
    const filename = `security-audit-${Date.now()}.${format}`

    return new Response(exportContent, {
      headers: {
        'Content-Type': mimeType,
        'Content-Disposition': `attachment; filename="${filename}"`,
      },
    })

  } catch (error) {
    console.error("导出安全审计日志失败:", error)
    throw error instanceof Error ? error : ErrorFactory.internal("导出安全审计日志失败")
  }
})

/**
 * 获取导出MIME类型
 */
function getExportMimeType(format: string): string {
  switch (format) {
    case 'csv':
      return 'text/csv'
    case 'excel':
      return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    case 'pdf':
      return 'application/pdf'
    default:
      return 'text/plain'
  }
}

/**
 * 获取用于导出的安全审计数据
 */
async function getSecurityAuditDataForExport(filters: {
  search: string
  severity: string
  eventType: string
  startDate?: string | null
  endDate?: string | null
}) {
  console.log("📊 获取导出数据...", filters)

  // 生成模拟的安全审计数据
  const events = []
  const now = new Date()

  // 模拟不同类型的安全事件
  const eventTypes = [
    { type: 'login', description: '用户登录', severity: 'info', riskScore: 15 },
    { type: 'logout', description: '用户登出', severity: 'info', riskScore: 10 },
    { type: 'permission_denied', description: '权限拒绝', severity: 'warning', riskScore: 65 },
    { type: 'rate_limit', description: '速率限制触发', severity: 'warning', riskScore: 45 },
    { type: 'data_access', description: '敏感数据访问', severity: 'info', riskScore: 25 },
    { type: 'admin_action', description: '管理员操作', severity: 'info', riskScore: 30 },
    { type: 'security_violation', description: '安全违规检测', severity: 'error', riskScore: 85 }
  ]

  const mockUsers = [
    { email: '<EMAIL>', id: 'user_1' },
    { email: '<EMAIL>', id: 'user_2' },
    { email: '<EMAIL>', id: 'user_3' },
    { email: '<EMAIL>', id: 'user_4' }
  ]

  const mockIPs = ['*************', '*********', '***********', '***********', '************']

  // 生成过去30天的数据
  for (let i = 0; i < 100; i++) {
    const timestamp = new Date(now.getTime() - Math.random() * 30 * 24 * 60 * 60 * 1000)
    const eventTemplate = eventTypes[Math.floor(Math.random() * eventTypes.length)]
    const user = mockUsers[Math.floor(Math.random() * mockUsers.length)]
    const ip = mockIPs[Math.floor(Math.random() * mockIPs.length)]

    const event = {
      id: `event_${i}_${Date.now()}`,
      timestamp: timestamp.toISOString(),
      eventType: eventTemplate.type,
      severity: eventTemplate.severity,
      userId: user.id,
      userEmail: user.email,
      ipAddress: ip,
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      resource: `/api/${eventTemplate.type.replace('_', '/')}`,
      action: eventTemplate.description,
      description: `${eventTemplate.description} - ${user.email}`,
      riskScore: eventTemplate.riskScore + Math.floor(Math.random() * 20) - 10,
      details: {
        timestamp: timestamp.toISOString(),
        source: 'security_monitor',
        category: eventTemplate.type
      }
    }

    events.push(event)
  }

  // 应用筛选条件
  let filteredEvents = events

  if (filters.startDate && filters.endDate) {
    const start = new Date(filters.startDate)
    const end = new Date(filters.endDate)
    filteredEvents = filteredEvents.filter(event => {
      const eventDate = new Date(event.timestamp)
      return eventDate >= start && eventDate <= end
    })
  }

  if (filters.search) {
    const searchLower = filters.search.toLowerCase()
    filteredEvents = filteredEvents.filter(event =>
      event.userEmail.toLowerCase().includes(searchLower) ||
      event.ipAddress.includes(searchLower) ||
      event.description.toLowerCase().includes(searchLower) ||
      event.eventType.toLowerCase().includes(searchLower)
    )
  }

  if (filters.severity !== 'all') {
    filteredEvents = filteredEvents.filter(event => event.severity === filters.severity)
  }

  if (filters.eventType !== 'all') {
    filteredEvents = filteredEvents.filter(event => event.eventType === filters.eventType)
  }

  // 按时间倒序排列
  filteredEvents.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())

  return {
    events: filteredEvents,
    metadata: {
      exportDate: new Date().toISOString(),
      totalEvents: filteredEvents.length,
      filters: filters,
      dateRange: {
        start: filters.startDate || '不限',
        end: filters.endDate || '不限'
      }
    }
  }
}

/**
 * 生成导出内容
 */
async function generateExportContent(data: any, format: string): Promise<string | Buffer> {
  switch (format) {
    case 'csv':
      return generateCSVExport(data)
    case 'excel':
      return await generateExcelExport(data)
    case 'pdf':
      return await generatePDFExport(data)
    default:
      throw new Error(`不支持的导出格式: ${format}`)
  }
}

/**
 * 生成CSV导出
 */
function generateCSVExport(data: any): string {
  const { events, metadata } = data
  
  let csv = 'ID,时间,事件类型,严重程度,用户邮箱,IP地址,资源,操作,描述,风险评分\n'
  
  events.forEach((event: any) => {
    const row = [
      event.id,
      new Date(event.timestamp).toLocaleString('zh-CN'),
      event.eventType,
      event.severity,
      event.userEmail || '',
      event.ipAddress,
      event.resource || '',
      event.action || '',
      `"${event.description.replace(/"/g, '""')}"`, // 转义CSV中的引号
      event.riskScore
    ].join(',')
    
    csv += row + '\n'
  })
  
  // 添加元数据
  csv += '\n\n# 导出信息\n'
  csv += `# 导出时间,${metadata.exportDate}\n`
  csv += `# 事件总数,${metadata.totalEvents}\n`
  csv += `# 时间范围,${metadata.dateRange.start} 至 ${metadata.dateRange.end}\n`
  
  return csv
}

/**
 * 生成Excel导出（模拟）
 */
async function generateExcelExport(data: any): Promise<Buffer> {
  // 在实际环境中，这里应该使用xlsx库生成真正的Excel文件
  // 现在返回一个CSV格式的数据作为模拟
  const csvContent = generateCSVExport(data)
  return Buffer.from(csvContent, 'utf8')
}

/**
 * 生成PDF导出（模拟）
 */
async function generatePDFExport(data: any): Promise<Buffer> {
  const { events, metadata } = data
  
  // 在实际环境中，这里应该使用puppeteer或类似工具生成PDF
  // 现在返回一个简单的文本格式作为模拟
  let pdfContent = `聆花景泰蓝工艺品管理系统 - 安全审计日志报告

导出时间: ${new Date(metadata.exportDate).toLocaleString('zh-CN')}
事件总数: ${metadata.totalEvents}
时间范围: ${metadata.dateRange.start} 至 ${metadata.dateRange.end}

==========================================

安全事件详情:

`

  events.forEach((event: any, index: number) => {
    pdfContent += `${index + 1}. ${event.description}
   时间: ${new Date(event.timestamp).toLocaleString('zh-CN')}
   类型: ${event.eventType} | 严重程度: ${event.severity}
   用户: ${event.userEmail || '未知'} | IP: ${event.ipAddress}
   风险评分: ${event.riskScore}/100
   
`
  })

  pdfContent += `
==========================================

报告说明:
- 此报告包含系统记录的所有安全相关事件
- 风险评分基于事件类型、严重程度和行为模式计算
- 建议定期审查高风险事件并采取相应措施

报告生成时间: ${new Date().toLocaleString('zh-CN')}
`

  return Buffer.from(pdfContent, 'utf8')
}