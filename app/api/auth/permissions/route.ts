import { NextRequest, NextResponse } from "next/server"
import { getToken } from "next-auth/jwt"
import { getUserPermissions } from "@/lib/auth/auth-middleware"
import { with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, UnifiedErrorHandler, ErrorFactory } from "@/lib/api/unified-error-handler"

/**
 * 获取当前用户的权限
 */
export const GET = withErrorHandler(async (req: NextRequest) => {
  try {
    // 获取用户Token
    const token = await getToken({ req })
    
    if (!token || !token.id) {
      throw ErrorFactory.unauthorized()
    }
    
    // 获取用户权限
    const permissions = await getUserPermissions(token.id as string)
    
    return UnifiedErrorHandler.success(permissions, "获取用户权限成功")
  } catch (error) {
    console.error("获取用户权限失败:", error)
    throw error instanceof Error ? error : ErrorFactory.internal("获取用户权限失败")
  }
})
