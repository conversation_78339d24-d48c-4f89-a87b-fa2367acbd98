import { NextRequest, NextResponse } from "next/server"
import prisma from "@/lib/db"
import { withEnhancedPermission, PERMISSIONS } from "@/lib/auth/enhanced-auth-middleware"
import { with<PERSON><PERSON>r<PERSON><PERSON><PERSON>, UnifiedErrorHandler, ErrorFactory } from "@/lib/api/unified-error-handler"

/**
 * 获取单个员工详情
 */
export const GET = withErrorHandler(async (req: NextRequest, { params }: { params: Promise<{ id: string }> }) => {
  try {
    console.log("🔍 获取员工详情API被调用")

    // 权限验证 - 需要员工查看权限
    const permissionCheck = await withEnhancedPermission(req, PERMISSIONS.EMPLOYEES_VIEW)
    if (permissionCheck) return permissionCheck

    // 确保params是已解析的
    const resolvedParams = await params
    const { id: idParam } = resolvedParams || {}
    const id = Number.parseInt(idParam)

    if (isNaN(id)) {
      throw ErrorFactory.validation("无效的员工ID")
    }

    const employee = await prisma.employee.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        }
      }
    })

    if (!employee) {
      throw ErrorFactory.notFound("员工", id.toString())
    }

    return UnifiedErrorHandler.success(employee, `成功获取员工详情: ${employee.name}`)
  } catch (error) {
    console.error("获取员工详情失败:", error)
    throw error instanceof Error ? error : ErrorFactory.database("获取员工详情", error)
  }
})

/**
 * 更新员工信息
 */
export const PUT = withErrorHandler(async (req: NextRequest, { params }: { params: Promise<{ id: string }> }) => {
  try {
    console.log("🔍 更新员工信息API被调用")

    // 权限验证 - 需要员工更新权限
    const permissionCheck = await withEnhancedPermission(req, PERMISSIONS.EMPLOYEES_UPDATE)
    if (permissionCheck) return permissionCheck

    // 确保params是已解析的
    const resolvedParams = await params
    const { id: idParam } = resolvedParams || {}
    const id = Number.parseInt(idParam)

    if (isNaN(id)) {
      throw ErrorFactory.validation("无效的员工ID")
    }

    const data = await req.json()

    // 验证必填字段
    if (data.name && !data.name.trim()) {
      throw ErrorFactory.requiredField("员工姓名")
    }
    if (data.position && !data.position.trim()) {
      throw ErrorFactory.requiredField("职位")
    }
    if (data.dailySalary !== undefined && (isNaN(Number.parseFloat(data.dailySalary)) || Number.parseFloat(data.dailySalary) < 0)) {
      throw ErrorFactory.validation("日薪必须是有效的非负数字")
    }

    // 验证邮箱格式（如果提供）
    if (data.email && data.email.trim() && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
      throw ErrorFactory.validation("邮箱格式不正确")
    }

    // 检查员工是否存在
    const existingEmployee = await prisma.employee.findUnique({
      where: { id }
    })

    if (!existingEmployee) {
      throw ErrorFactory.notFound("员工", id.toString())
    }

    const employee = await prisma.employee.update({
      where: { id },
      data: {
        ...(data.name && { name: data.name.trim() }),
        ...(data.position && { position: data.position.trim() }),
        ...(data.phone !== undefined && { phone: data.phone?.trim() || null }),
        ...(data.email !== undefined && { email: data.email?.trim() || null }),
        ...(data.dailySalary !== undefined && { dailySalary: Number.parseFloat(data.dailySalary) }),
        ...(data.status && { status: data.status }),
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        }
      }
    })

    return UnifiedErrorHandler.success(employee, `成功更新员工信息: ${employee.name}`)
  } catch (error) {
    console.error("更新员工信息失败:", error)
    throw error instanceof Error ? error : ErrorFactory.database("更新员工信息", error)
  }
})

/**
 * 删除员工
 */
export const DELETE = withErrorHandler(async (req: NextRequest, { params }: { params: Promise<{ id: string }> }) => {
  try {
    console.log("🔍 删除员工API被调用")

    // 权限验证 - 需要员工删除权限
    const permissionCheck = await withEnhancedPermission(req, PERMISSIONS.EMPLOYEES_DELETE)
    if (permissionCheck) return permissionCheck

    // 确保params是已解析的
    const resolvedParams = await params
    const { id: idParam } = resolvedParams || {}
    const id = Number.parseInt(idParam)

    if (isNaN(id)) {
      throw ErrorFactory.validation("无效的员工ID")
    }

    // 检查员工是否存在
    const existingEmployee = await prisma.employee.findUnique({
      where: { id },
      include: {
        user: true,
        salaryRecords: true,
        salaryAdjustments: true
      }
    })

    if (!existingEmployee) {
      throw ErrorFactory.notFound("员工", id.toString())
    }

    // 检查是否有关联数据，如果有则不允许删除
    if (existingEmployee.salaryRecords.length > 0) {
      throw ErrorFactory.businessRule("无法删除员工，存在薪资记录", "has_salary_records")
    }

    // 如果有关联用户，先解除关联
    if (existingEmployee.user) {
      await prisma.user.update({
        where: { id: existingEmployee.user.id },
        data: { employeeId: null }
      })
    }

    await prisma.employee.delete({
      where: { id },
    })

    return UnifiedErrorHandler.success(
      { id, name: existingEmployee.name },
      `成功删除员工: ${existingEmployee.name}`
    )
  } catch (error) {
    console.error("删除员工失败:", error)
    throw error instanceof Error ? error : ErrorFactory.database("删除员工", error)
  }
})
