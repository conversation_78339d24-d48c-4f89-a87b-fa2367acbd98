import { NextRequest, NextResponse } from "next/server"
import prisma from "@/lib/db"
import { withEnhancedPermission, PERMISSIONS } from "@/lib/auth/enhanced-auth-middleware"
import { with<PERSON><PERSON>r<PERSON><PERSON><PERSON>, UnifiedErrorHandler, ErrorFactory } from "@/lib/api/unified-error-handler"

/**
 * 获取员工排班记录
 */
export const GET = withErrorHandler(async (
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) => {
  try {
    console.log("🔍 获取员工排班记录API被调用")

    // 权限验证 - 需要员工查看权限
    const permissionCheck = await withEnhancedPermission(req, PERMISSIONS.EMPLOYEES_VIEW)
    if (permissionCheck) return permissionCheck

    // 确保params是已解析的
    const resolvedParams = await params
    const { id: idParam } = resolvedParams || {}
    const id = parseInt(idParam)

    if (isNaN(id)) {
      throw ErrorFactory.validation("无效的员工ID")
    }

    // 验证员工是否存在
    const employee = await prisma.employee.findUnique({
      where: { id },
      select: { id: true, name: true }
    })

    if (!employee) {
      throw ErrorFactory.notFound("员工", id.toString())
    }

    // 获取查询参数
    const { searchParams } = new URL(req.url)
    const limit = searchParams.get('limit') ? parseInt(searchParams.get('limit')) : undefined
    const startDate = searchParams.get('startDate') ? new Date(searchParams.get('startDate')) : undefined
    const endDate = searchParams.get('endDate') ? new Date(searchParams.get('endDate')) : undefined

    // 验证查询参数
    if (limit && (isNaN(limit) || limit <= 0)) {
      throw ErrorFactory.validation("限制数量必须是正整数")
    }

    if (startDate && isNaN(startDate.getTime())) {
      throw ErrorFactory.validation("开始日期格式不正确")
    }

    if (endDate && isNaN(endDate.getTime())) {
      throw ErrorFactory.validation("结束日期格式不正确")
    }

    // 构建查询条件
    const whereClause: any = {
      employeeId: id,
    }

    if (startDate && endDate) {
      if (startDate > endDate) {
        throw ErrorFactory.validation("开始日期不能晚于结束日期")
      }
      whereClause.date = {
        gte: startDate,
        lte: endDate,
      }
    } else if (startDate) {
      whereClause.date = {
        gte: startDate,
      }
    } else if (endDate) {
      whereClause.date = {
        lte: endDate,
      }
    }

    // 查询排班记录
    const schedules = await prisma.schedule.findMany({
      where: whereClause,
      orderBy: {
        date: 'desc',
      },
      take: limit,
    })

    return UnifiedErrorHandler.success({
      schedules,
      employee: { id: employee.id, name: employee.name },
      summary: {
        totalCount: schedules.length,
        dateRange: startDate && endDate ? { startDate, endDate } : null,
        limit: limit || null
      }
    }, `成功获取员工 ${employee.name} 的排班记录，共 ${schedules.length} 条`)

  } catch (error) {
    console.error("获取员工排班记录失败:", error)
    throw error instanceof Error ? error : ErrorFactory.database("获取员工排班记录", error)
  }
})
