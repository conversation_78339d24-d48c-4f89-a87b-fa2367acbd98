import { NextRequest, NextResponse } from 'next/server'
import prisma from '@/lib/db'
import { withEnhancedPermission, PERMISSIONS } from "@/lib/auth/enhanced-auth-middleware"
import { with<PERSON><PERSON>r<PERSON><PERSON><PERSON>, UnifiedErrorHandler, ErrorFactory } from "@/lib/api/unified-error-handler"

/**
 * 获取员工绩效数据
 */
export const GET = withErrorHandler(async (req: NextRequest) => {
  try {
    console.log("🔍 获取员工绩效数据API被调用")

    // 权限验证 - 需要员工查看权限（绩效数据属于员工管理）
    const permissionCheck = await withEnhancedPermission(req, PERMISSIONS.EMPLOYEES_VIEW)
    if (permissionCheck) return permissionCheck

    // 查询员工绩效数据
    const employees = await prisma.user.findMany({
      where: {
        role: 'EMPLOYEE'
      },
      include: {
        salaryRecords: {
          take: 1,
          orderBy: { createdAt: 'desc' }
        }
      }
    })

    // 计算绩效数据
    const performanceData = employees.map(employee => {
      const latestSalary = employee.salaryRecords[0]
      
      return {
        id: employee.id,
        name: employee.name || '未知员工',
        position: employee.position || '员工',
        performance: {
          sales: Math.floor(Math.random() * 50000) + 10000, // 模拟销售额
          orders: Math.floor(Math.random() * 50) + 10, // 模拟订单数
          rating: (Math.random() * 2 + 3).toFixed(1), // 3-5分评分
          efficiency: Math.floor(Math.random() * 30) + 70 // 70-100%效率
        },
        salary: latestSalary?.baseSalary || 0,
        bonus: latestSalary?.bonus || 0,
        attendance: Math.floor(Math.random() * 5) + 20, // 20-25天出勤
        lastActive: employee.updatedAt.toISOString()
      }
    })

    return UnifiedErrorHandler.success({
      performanceData,
      summary: {
        totalEmployees: performanceData.length,
        averageRating: performanceData.length > 0 ?
          (performanceData.reduce((sum, emp) => sum + parseFloat(emp.performance.rating), 0) / performanceData.length).toFixed(1) : '0.0',
        totalSales: performanceData.reduce((sum, emp) => sum + emp.performance.sales, 0),
        averageEfficiency: performanceData.length > 0 ?
          Math.round(performanceData.reduce((sum, emp) => sum + emp.performance.efficiency, 0) / performanceData.length) : 0
      }
    }, `成功获取员工绩效数据，共 ${performanceData.length} 名员工`)

  } catch (error) {
    console.error('获取员工绩效数据失败:', error)
    throw error instanceof Error ? error : ErrorFactory.database("获取员工绩效数据", error)
  }
})
