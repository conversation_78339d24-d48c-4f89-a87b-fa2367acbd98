import { NextRequest, NextResponse } from "next/server"
import prisma from "@/lib/db"
import { withEnhancedPermission, PERMISSIONS } from "@/lib/auth/enhanced-auth-middleware"
import { with<PERSON><PERSON>r<PERSON><PERSON><PERSON>, UnifiedErrorHandler, ErrorFactory } from "@/lib/api/unified-error-handler"

/**
 * 获取员工列表
 */
export const GET = withErrorHandler(async (req: NextRequest) => {
  try {
    console.log("🔍 获取员工列表API被调用")

    // 权限验证 - 需要员工查看权限
    const permissionCheck = await withEnhancedPermission(req, PERMISSIONS.EMPLOYEES_VIEW)
    if (permissionCheck) return permissionCheck

    const employees = await prisma.employee.findMany({
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        }
      },
      orderBy: {
        id: "asc",
      },
    })

    return UnifiedErrorHandler.success(employees, `成功获取 ${employees.length} 个员工记录`)
  } catch (error) {
    console.error("获取员工列表失败:", error)
    throw ErrorFactory.database("获取员工列表", error)
  }
})

/**
 * 创建员工
 */
export const POST = withErrorHandler(async (req: NextRequest) => {
  try {
    console.log("🔍 创建员工API被调用")

    // 权限验证 - 需要员工创建权限
    const permissionCheck = await withEnhancedPermission(req, PERMISSIONS.EMPLOYEES_CREATE)
    if (permissionCheck) return permissionCheck

    const data = await req.json()

    // 验证必填字段
    if (!data.name) {
      throw ErrorFactory.requiredField("员工姓名")
    }
    if (!data.position) {
      throw ErrorFactory.requiredField("职位")
    }
    if (!data.dailySalary || isNaN(Number.parseFloat(data.dailySalary))) {
      throw ErrorFactory.validation("日薪必须是有效的数字")
    }

    // 验证邮箱格式（如果提供）
    if (data.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
      throw ErrorFactory.validation("邮箱格式不正确")
    }

    const employee = await prisma.employee.create({
      data: {
        name: data.name.trim(),
        position: data.position.trim(),
        phone: data.phone?.trim() || null,
        email: data.email?.trim() || null,
        dailySalary: Number.parseFloat(data.dailySalary),
        status: data.status || "active",
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        }
      }
    })

    return UnifiedErrorHandler.success(employee, `成功创建员工: ${employee.name}`)
  } catch (error) {
    console.error("创建员工失败:", error)
    throw error instanceof Error ? error : ErrorFactory.internal("创建员工失败")
  }
})
