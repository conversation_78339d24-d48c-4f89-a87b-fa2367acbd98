import { NextRequest, NextResponse } from "next/server"
import { withEnhancedPermission, PERMISSIONS } from "@/lib/auth/enhanced-auth-middleware"
import { with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, UnifiedErrorHandler, ErrorFactory } from "@/lib/api/unified-error-handler"
import { withRateLimit, RATE_LIMIT_CONFIGS } from "@/lib/api/rate-limiter"
import { RequestValidator, ValidationTemplates } from "@/lib/api/request-validator"
import { 
  getFinancialTransactions, 
  createFinancialTransaction 
} from "@/lib/actions/finance-actions"

/**
 * 获取财务交易记录
 */
export const GET = withErrorHandler(async (req: NextRequest) => {
  try {
    // 应用速率限制 - 财务查询使用标准限制
    const rateLimitCheck = await withRateLimit(RATE_LIMIT_CONFIGS.STANDARD)(req)
    if (rateLimitCheck) return rateLimitCheck

    // 权限验证 - 需要财务查看权限
    const permissionCheck = await withEnhancedPermission(req, 'finance.view')
    if (permissionCheck) return permissionCheck

    const { searchParams } = new URL(req.url)
    const accountId = searchParams.get("accountId") ? parseInt(searchParams.get("accountId")) : undefined
    const categoryId = searchParams.get("categoryId") ? parseInt(searchParams.get("categoryId")) : undefined
    const type = searchParams.get("type") || undefined
    const startDate = searchParams.get("startDate") || undefined
    const endDate = searchParams.get("endDate") || undefined
    const limit = searchParams.get("limit") ? parseInt(searchParams.get("limit")) : 50
    const offset = searchParams.get("offset") ? parseInt(searchParams.get("offset")) : 0

    const transactions = await getFinancialTransactions(
      accountId,
      categoryId,
      type,
      startDate,
      endDate,
      limit,
      offset
    )
    return UnifiedErrorHandler.success(transactions, "获取财务交易记录成功")
  } catch (error) {
    console.error("Error fetching financial transactions:", error)
    throw error instanceof Error ? error : ErrorFactory.internal("获取财务交易记录失败")
  }
})

/**
 * 创建财务交易记录
 */
export const POST = withErrorHandler(async (req: NextRequest) => {
  try {
    // 应用速率限制 - 财务创建使用严格限制
    const rateLimitCheck = await withRateLimit(RATE_LIMIT_CONFIGS.STRICT)(req)
    if (rateLimitCheck) return rateLimitCheck

    // 权限验证 - 需要财务创建权限
    const permissionCheck = await withEnhancedPermission(req, 'finance.create')
    if (permissionCheck) return permissionCheck

    // 数据验证
    const validation = await RequestValidator.validateRequest(req, [
      { field: 'amount', required: true, type: 'number', min: 0.01 },
      { field: 'description', required: true, type: 'string', minLength: 1, maxLength: 500 },
      { field: 'type', required: true, type: 'string', enum: ['income', 'expense'] },
      { field: 'accountId', required: true, type: 'number', min: 1 },
      { field: 'categoryId', required: false, type: 'number', min: 1 },
      { field: 'transactionDate', required: false, type: 'date' }
    ])
    
    if (!validation.isValid) {
      throw ErrorFactory.validation(
        `数据验证失败: ${validation.errors.join(', ')}`,
        undefined,
        { errors: validation.errors }
      )
    }

    const data = validation.sanitizedData
    
    const transaction = await createFinancialTransaction(data)
    return UnifiedErrorHandler.success(transaction, "财务交易记录创建成功")
  } catch (error) {
    console.error("Error creating financial transaction:", error)
    throw error instanceof Error ? error : ErrorFactory.internal("创建财务交易记录失败")
  }
})
