import { NextRequest, NextResponse } from "next/server"
import prisma from "@/lib/db"
import { withEnhancedPermission, PERMISSIONS } from "@/lib/auth/enhanced-auth-middleware"
import { with<PERSON>rror<PERSON>and<PERSON>, UnifiedErrorHandler, ErrorFactory } from "@/lib/api/unified-error-handler"

/**
 * 获取薪资计算数据（极敏感财务信息）
 */
export const GET = withErrorHandler(async (req: NextRequest) => {
  try {
    console.log("🔍 获取薪资计算数据API被调用")

    // 权限验证 - 需要员工查看权限（薪资计算属于员工管理的敏感数据）
    const permissionCheck = await withEnhancedPermission(req, PERMISSIONS.EMPLOYEES_VIEW)
    if (permissionCheck) return permissionCheck

    const { searchParams } = new URL(req.url)
    const yearParam = searchParams.get("year")
    const monthParam = searchParams.get("month")

    // 验证必填参数
    if (!yearParam) {
      throw ErrorFactory.requiredField("年份")
    }
    if (!monthParam) {
      throw ErrorFactory.requiredField("月份")
    }

    // 验证参数格式
    const year = parseInt(yearParam)
    const month = parseInt(monthParam)

    if (isNaN(year) || year < 2020 || year > 2030) {
      throw ErrorFactory.validation("年份必须在2020-2030之间")
    }
    if (isNaN(month) || month < 1 || month > 12) {
      throw ErrorFactory.validation("月份必须在1-12之间")
    }

    // 计算月份的开始和结束日期
    const startDate = new Date(Number.parseInt(year), Number.parseInt(month) - 1, 1)
    const endDate = new Date(Number.parseInt(year), Number.parseInt(month), 0)

    // 获取所有员工
    const employees = await prisma.employee.findMany({
      where: {
        status: "active",
      },
    })

    // 获取系统设置
    const settings = await prisma.systemSetting.findFirst()

    // 计算每个员工的薪酬
    const payrollData = await Promise.all(
      employees.map(async (employee) => {
        // 1. 计算基本工资（日薪 * 出勤天数）
        const schedules = await prisma.schedule.findMany({
          where: {
            employeeId: employee.id,
            date: {
              gte: startDate,
              lte: endDate,
            },
          },
        })
        const workDays = schedules.length
        const baseSalary = employee.dailySalary * workDays

        // 2. 计算手作团建费
        const workshops = await prisma.workshop.findMany({
          where: {
            employeeId: employee.id,
            date: {
              gte: startDate,
              lte: endDate,
            },
          },
        })

        let workshopFee = 0
        workshops.forEach((workshop) => {
          if (workshop.role === "teacher") {
            workshopFee += settings?.teacherWorkshopFee || 200
          } else {
            workshopFee += settings?.assistantWorkshopFee || 130
          }
        })

        // 3. 计算咖啡店提成
        const coffeeShifts = await prisma.coffeeShopShift.findMany({
          where: {
            employeeId: employee.id,
            coffeeShopSale: {
              date: {
                gte: startDate,
                lte: endDate,
              },
            },
          },
          include: {
            coffeeShopSale: true,
          },
        })

        let coffeeSalesCommission = 0
        for (const shift of coffeeShifts) {
          // 获取当天所有值班员工数量
          const totalShifts = await prisma.coffeeShopShift.count({
            where: {
              coffeeShopSaleId: shift.coffeeShopSaleId,
            },
          })

          // 计算提成：总销售额 * 提成比例 / 值班员工数
          const commissionRate = settings?.coffeeSalesCommissionRate || 20
          const commission = (shift.coffeeShopSale.totalSales * (commissionRate / 100)) / totalShifts
          coffeeSalesCommission += commission
        }

        // 4. 计算珐琅馆销售提成
        const gallerySales = await prisma.gallerySale.findMany({
          where: {
            employeeId: employee.id,
            date: {
              gte: startDate,
              lte: endDate,
            },
          },
        })

        const commissionRate = settings?.gallerySalesCommissionRate || 10
        const gallerySalesCommission = gallerySales.reduce(
          (sum, sale) => sum + sale.totalAmount * (commissionRate / 100),
          0,
        )

        // 5. 计算计件工费
        const pieceWorks = await prisma.pieceWork.findMany({
          where: {
            employeeId: employee.id,
            date: {
              gte: startDate,
              lte: endDate,
            },
          },
          include: {
            details: true,
          },
        })

        // 分别计算配饰和点蓝工费
        let accessoryFee = 0
        let enamellingFee = 0

        pieceWorks.forEach((pieceWork) => {
          if (pieceWork.workType === "accessory") {
            accessoryFee += pieceWork.totalAmount
          } else if (pieceWork.workType === "enamelling") {
            enamellingFee += pieceWork.totalAmount
          }
        })

        // 6. 计算总薪酬
        const totalSalary =
          baseSalary + workshopFee + coffeeSalesCommission + gallerySalesCommission + accessoryFee + enamellingFee

        return {
          id: employee.id,
          employee: employee.name,
          position: employee.position,
          dailySalary: employee.dailySalary,
          workDays,
          baseSalary,
          workshopFee,
          coffeeSalesCommission,
          gallerySalesCommission,
          accessoryFee,
          enamellingFee,
          totalSalary,
        }
      }),
    )

    return UnifiedErrorHandler.success({
      payrollData,
      summary: {
        totalEmployees: payrollData.length,
        period: `${year}-${month.toString().padStart(2, '0')}`,
        totalPayroll: payrollData.reduce((sum, emp) => sum + emp.totalSalary, 0),
        averageSalary: payrollData.length > 0 ?
          Math.round(payrollData.reduce((sum, emp) => sum + emp.totalSalary, 0) / payrollData.length) : 0
      }
    }, `成功计算薪资数据，共 ${payrollData.length} 名员工，${year}年${month}月`)

  } catch (error) {
    console.error("计算薪资数据失败:", error)
    throw error instanceof Error ? error : ErrorFactory.database("计算薪资数据", error)
  }
})
