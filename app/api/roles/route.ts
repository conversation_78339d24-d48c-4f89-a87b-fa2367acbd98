import { NextRequest, NextResponse } from "next/server"
import prisma from "@/lib/db"
import { withEnhancedPermission, PERMISSIONS } from "@/lib/auth/enhanced-auth-middleware"
import { with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, UnifiedErrorHandler, ErrorFactory } from "@/lib/api/unified-error-handler"

/**
 * 获取所有角色
 */
export const GET = withErrorHandler(async (req: NextRequest) => {
  try {
    console.log("🔍 获取角色列表API被调用")

    // 权限验证 - 需要角色查看权限
    const permissionCheck = await withEnhancedPermission(req, PERMISSIONS.ROLES_VIEW)
    if (permissionCheck) return permissionCheck

    // 从数据库获取角色
    const roles = await prisma.role.findMany({
      include: {
        _count: {
          select: {
            userRoles: true,
          },
        },
        rolePermissions: {
          include: {
            permission: true,
          },
        },
      },
      orderBy: {
        id: "asc",
      },
    });

    // 格式化返回数据
    const formattedRoles = roles.map(role => ({
      id: role.id,
      name: role.name,
      code: role.code,
      description: role.description,
      isSystem: role.isSystem,
      userCount: role._count.userRoles,
      createdAt: role.createdAt,
      updatedAt: role.updatedAt,
      permissions: role.rolePermissions.map(rp => rp.permission),
    }));

    return UnifiedErrorHandler.success({
      roles: formattedRoles,
      summary: {
        totalCount: formattedRoles.length,
        systemRoles: formattedRoles.filter(r => r.isSystem).length,
        customRoles: formattedRoles.filter(r => !r.isSystem).length
      }
    }, `成功获取角色列表，共 ${formattedRoles.length} 个角色`);
  } catch (error) {
    console.error("获取角色失败:", error);
    throw error instanceof Error ? error : ErrorFactory.database("获取角色列表", error);
  }
})

/**
 * 创建新角色
 */
export const POST = withErrorHandler(async (req: NextRequest) => {
  try {
    console.log("🔍 创建角色API被调用")

    // 权限验证 - 需要角色创建权限
    const permissionCheck = await withEnhancedPermission(req, PERMISSIONS.ROLES_CREATE)
    if (permissionCheck) return permissionCheck

    // 获取请求数据
    const data = await req.json()
    const { name, code, description, permissionIds } = data

    // 验证必填字段
    if (!name || !name.trim()) {
      throw ErrorFactory.requiredField("角色名称")
    }
    if (!code || !code.trim()) {
      throw ErrorFactory.requiredField("角色代码")
    }

    // 验证角色代码格式
    if (!/^[a-z_]+$/.test(code)) {
      throw ErrorFactory.validation("角色代码只能包含小写字母和下划线")
    }

    // 验证权限ID数组
    if (permissionIds && !Array.isArray(permissionIds)) {
      throw ErrorFactory.validation("权限ID必须是数组")
    }

    // 检查角色代码是否已存在
    const existingRole = await prisma.role.findUnique({
      where: { code: code.trim() },
    })

    if (existingRole) {
      throw ErrorFactory.validation(`角色代码 "${code}" 已被使用`)
    }

    // 验证权限ID是否存在
    if (permissionIds && permissionIds.length > 0) {
      const validPermissions = await prisma.permission.findMany({
        where: { id: { in: permissionIds } }
      })

      if (validPermissions.length !== permissionIds.length) {
        throw ErrorFactory.validation("包含无效的权限ID")
      }
    }

    // 创建角色
    const role = await prisma.role.create({
      data: {
        name: name.trim(),
        code: code.trim(),
        description: description?.trim() || null,
        isSystem: false,
      },
    })

    // 分配权限
    if (permissionIds && permissionIds.length > 0) {
      await prisma.rolePermission.createMany({
        data: permissionIds.map((permissionId: number) => ({
          roleId: role.id,
          permissionId,
        })),
        skipDuplicates: true,
      })
    }

    // 获取完整角色信息
    const createdRole = await prisma.role.findUnique({
      where: { id: role.id },
      include: {
        rolePermissions: {
          include: {
            permission: true,
          },
        },
      },
    })

    // 格式化角色数据
    const formattedRole = {
      id: createdRole?.id,
      name: createdRole?.name,
      code: createdRole?.code,
      description: createdRole?.description,
      isSystem: createdRole?.isSystem,
      createdAt: createdRole?.createdAt,
      updatedAt: createdRole?.updatedAt,
      permissions: createdRole?.rolePermissions.map(rp => rp.permission) || [],
    }

    return UnifiedErrorHandler.success(
      formattedRole,
      `成功创建角色: ${name}`
    )

  } catch (error) {
    console.error("创建角色失败:", error)
    throw error instanceof Error ? error : ErrorFactory.database("创建角色", error)
  }
})
