import { NextRequest } from "next/server"
import { withEnhancedPermission, PERMISSIONS } from "@/lib/auth/enhanced-auth-middleware"
import { withE<PERSON>r<PERSON><PERSON><PERSON>, UnifiedErrorHandler, ErrorFactory } from "@/lib/api/unified-error-handler"
import { getSalaryAdjustments, createSalaryAdjustment } from "@/lib/actions/employee-actions"

/**
 * 获取薪资调整记录
 */
export const GET = withErrorHandler(async (req: NextRequest) => {
  try {
    console.log("🔍 获取薪资调整记录API被调用")

    // 权限验证 - 需要员工查看权限（薪资调整属于员工管理）
    const permissionCheck = await withEnhancedPermission(req, PERMISSIONS.EMPLOYEES_VIEW)
    if (permissionCheck) return permissionCheck

    const { searchParams } = new URL(req.url)
    const salaryRecordIdParam = searchParams.get('salaryRecordId')

    // 验证salaryRecordId参数（必需）
    if (!salaryRecordIdParam) {
      throw ErrorFactory.validation("薪资记录ID是必需的")
    }

    const salaryRecordId = parseInt(salaryRecordIdParam)
    if (isNaN(salaryRecordId) || salaryRecordId <= 0) {
      throw ErrorFactory.validation("薪资记录ID必须是有效的正整数")
    }

    const salaryAdjustments = await getSalaryAdjustments(salaryRecordId)

    return UnifiedErrorHandler.success({
      salaryAdjustments,
      summary: {
        totalCount: salaryAdjustments.length,
        salaryRecordId: salaryRecordId
      }
    }, `成功获取薪资调整记录，共 ${salaryAdjustments.length} 条`)

  } catch (error) {
    console.error("获取薪资调整记录失败:", error)
    throw error instanceof Error ? error : ErrorFactory.database("获取薪资调整记录", error)
  }
})

/**
 * 创建薪资调整记录
 */
export const POST = withErrorHandler(async (req: NextRequest) => {
  try {
    console.log("🔍 创建薪资调整记录API被调用")

    // 权限验证 - 需要员工更新权限（创建薪资调整属于员工管理）
    const permissionCheck = await withEnhancedPermission(req, PERMISSIONS.EMPLOYEES_UPDATE)
    if (permissionCheck) return permissionCheck

    const data = await req.json()

    // 基础验证
    if (!data) {
      throw ErrorFactory.validation("请求数据不能为空")
    }

    const salaryAdjustment = await createSalaryAdjustment(data)

    return UnifiedErrorHandler.success(
      salaryAdjustment,
      `成功创建薪资调整记录`
    )

  } catch (error) {
    console.error("创建薪资调整记录失败:", error)
    throw error instanceof Error ? error : ErrorFactory.database("创建薪资调整记录", error)
  }
})
