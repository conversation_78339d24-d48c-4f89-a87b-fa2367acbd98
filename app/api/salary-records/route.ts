import { NextRequest, NextResponse } from "next/server"
import { withEnhancedPermission, PERMISSIONS } from "@/lib/auth/enhanced-auth-middleware"
import { withErrorHandler, UnifiedErrorHandler, ErrorFactory } from "@/lib/api/unified-error-handler"
import { getSalaryRecords, createSalaryRecord } from "@/lib/actions/employee-actions"

/**
 * 获取薪资记录
 */
export const GET = withErrorHandler(async (req: NextRequest) => {
  try {
    console.log("🔍 获取薪资记录API被调用")

    // 权限验证 - 需要员工查看权限（薪资记录属于员工管理）
    const permissionCheck = await withEnhancedPermission(req, PERMISSIONS.EMPLOYEES_VIEW)
    if (permissionCheck) return permissionCheck

    const { searchParams } = new URL(req.url)
    const employeeIdParam = searchParams.get('employeeId')
    const yearParam = searchParams.get('year')
    const monthParam = searchParams.get('month')

    // 验证参数
    let employeeId: number | undefined
    let year: number | undefined
    let month: number | undefined

    if (employeeIdParam) {
      employeeId = parseInt(employeeIdParam)
      if (isNaN(employeeId) || employeeId <= 0) {
        throw ErrorFactory.validation("员工ID必须是有效的正整数")
      }
    }

    if (yearParam) {
      year = parseInt(yearParam)
      if (isNaN(year) || year < 2020 || year > 2030) {
        throw ErrorFactory.validation("年份必须在2020-2030之间")
      }
    }

    if (monthParam) {
      month = parseInt(monthParam)
      if (isNaN(month) || month < 1 || month > 12) {
        throw ErrorFactory.validation("月份必须在1-12之间")
      }
    }

    const salaryRecords = await getSalaryRecords(employeeId, year, month)

    return UnifiedErrorHandler.success({
      salaryRecords,
      summary: {
        totalCount: salaryRecords.length,
        filters: {
          employeeId: employeeId || null,
          year: year || null,
          month: month || null
        }
      }
    }, `成功获取薪资记录，共 ${salaryRecords.length} 条`)

  } catch (error) {
    console.error("获取薪资记录失败:", error)
    throw error instanceof Error ? error : ErrorFactory.database("获取薪资记录", error)
  }
})

/**
 * 创建薪资记录
 */
export const POST = withErrorHandler(async (req: NextRequest) => {
  try {
    console.log("🔍 创建薪资记录API被调用")

    // 权限验证 - 需要员工更新权限（创建薪资记录属于员工管理）
    const permissionCheck = await withEnhancedPermission(req, PERMISSIONS.EMPLOYEES_UPDATE)
    if (permissionCheck) return permissionCheck

    const data = await req.json()

    // 基础验证
    if (!data) {
      throw ErrorFactory.validation("请求数据不能为空")
    }

    const salaryRecord = await createSalaryRecord(data)

    return UnifiedErrorHandler.success(
      salaryRecord,
      `成功创建薪资记录`
    )

  } catch (error) {
    console.error("创建薪资记录失败:", error)
    throw error instanceof Error ? error : ErrorFactory.database("创建薪资记录", error)
  }
})
