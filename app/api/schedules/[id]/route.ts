import { NextRequest, NextResponse } from "next/server"
import { withEnhancedPermission, PERMISSIONS } from "@/lib/auth/enhanced-auth-middleware"
import { with<PERSON><PERSON>r<PERSON><PERSON><PERSON>, UnifiedErrorHandler, ErrorFactory } from "@/lib/api/unified-error-handler"
import { deleteSchedule } from "@/lib/actions/schedule-actions"
import prisma from "@/lib/db"

/**
 * 删除单个排班记录
 */
export const DELETE = withErrorHandler(async (
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) => {
  try {
    console.log("🔍 删除排班记录API被调用")

    // 权限验证 - 需要员工删除权限（删除排班属于员工管理）
    const permissionCheck = await withEnhancedPermission(req, PERMISSIONS.EMPLOYEES_DELETE)
    if (permissionCheck) return permissionCheck

    const resolvedParams = await params
    const { id: idParam } = resolvedParams || {}
    const id = parseInt(idParam)

    if (isNaN(id) || id <= 0) {
      throw ErrorFactory.validation("排班记录ID必须是有效的正整数")
    }

    // 检查排班记录是否存在
    const existingSchedule = await prisma.schedule.findUnique({
      where: { id },
      include: {
        employee: {
          select: {
            id: true,
            name: true
          }
        }
      }
    })

    if (!existingSchedule) {
      throw ErrorFactory.notFound("排班记录", id.toString())
    }

    // 使用服务器操作删除排班
    await deleteSchedule(id)

    return UnifiedErrorHandler.success(
      { deletedSchedule: existingSchedule },
      `成功删除排班记录: ${existingSchedule.employee.name} (${existingSchedule.date})`
    )

  } catch (error) {
    console.error("删除排班记录失败:", error)
    throw error instanceof Error ? error : ErrorFactory.database("删除排班记录", error)
  }
})
