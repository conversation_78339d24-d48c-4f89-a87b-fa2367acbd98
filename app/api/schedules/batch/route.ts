import { NextRequest, NextResponse } from "next/server"
import { withEnhancedPermission, PERMISSIONS } from "@/lib/auth/enhanced-auth-middleware"
import { with<PERSON>rror<PERSON>andler, UnifiedErrorHandler, ErrorFactory } from "@/lib/api/unified-error-handler"
import { createBatchSchedules } from "@/lib/actions/schedule-actions"

/**
 * 批量创建排班记录
 */
export const POST = withErrorHandler(async (req: NextRequest) => {
  try {
    console.log("🔍 批量创建排班记录API被调用")

    // 权限验证 - 需要员工更新权限（批量创建排班属于员工管理）
    const permissionCheck = await withEnhancedPermission(req, PERMISSIONS.EMPLOYEES_UPDATE)
    if (permissionCheck) return permissionCheck

    const data = await req.json()

    // 验证数据格式
    if (!Array.isArray(data)) {
      throw ErrorFactory.validation("请求数据必须是数组格式")
    }

    if (data.length === 0) {
      throw ErrorFactory.validation("排班数据不能为空")
    }

    if (data.length > 100) {
      throw ErrorFactory.validation("单次批量操作不能超过100条记录")
    }

    // 验证每个排班记录的基本字段
    for (let i = 0; i < data.length; i++) {
      const schedule = data[i]

      if (!schedule.employeeId) {
        throw ErrorFactory.validation(`第${i + 1}条记录缺少员工ID`)
      }
      if (!schedule.date) {
        throw ErrorFactory.validation(`第${i + 1}条记录缺少日期`)
      }
      if (!schedule.startTime) {
        throw ErrorFactory.validation(`第${i + 1}条记录缺少开始时间`)
      }
      if (!schedule.endTime) {
        throw ErrorFactory.validation(`第${i + 1}条记录缺少结束时间`)
      }

      // 验证员工ID格式
      const employeeId = Number.parseInt(schedule.employeeId)
      if (isNaN(employeeId) || employeeId <= 0) {
        throw ErrorFactory.validation(`第${i + 1}条记录的员工ID格式不正确`)
      }

      // 验证日期格式
      const scheduleDate = new Date(schedule.date)
      if (isNaN(scheduleDate.getTime())) {
        throw ErrorFactory.validation(`第${i + 1}条记录的日期格式不正确`)
      }

      // 验证时间格式
      const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/
      if (!timeRegex.test(schedule.startTime)) {
        throw ErrorFactory.validation(`第${i + 1}条记录的开始时间格式不正确，应为HH:MM`)
      }
      if (!timeRegex.test(schedule.endTime)) {
        throw ErrorFactory.validation(`第${i + 1}条记录的结束时间格式不正确，应为HH:MM`)
      }

      // 验证时间逻辑
      if (schedule.startTime >= schedule.endTime) {
        throw ErrorFactory.validation(`第${i + 1}条记录的开始时间必须早于结束时间`)
      }
    }

    const schedules = await createBatchSchedules(data)

    return UnifiedErrorHandler.success({
      schedules,
      summary: {
        totalCreated: schedules.length,
        requestedCount: data.length
      }
    }, `成功批量创建排班记录，共 ${schedules.length} 条`)

  } catch (error) {
    console.error("批量创建排班记录失败:", error)
    throw error instanceof Error ? error : ErrorFactory.database("批量创建排班记录", error)
  }
})
