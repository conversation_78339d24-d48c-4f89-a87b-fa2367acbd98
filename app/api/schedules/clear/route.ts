import { NextRequest, NextResponse } from "next/server"
import { withEnhancedPermission, PERMISSIONS } from "@/lib/auth/enhanced-auth-middleware"
import { withErrorHandler, UnifiedErrorHandler, ErrorFactory } from "@/lib/api/unified-error-handler"
import { clearAllSchedules } from "@/lib/actions/schedule-actions"
import prisma from "@/lib/db"

/**
 * 清空所有排班记录（危险操作）
 */
export const DELETE = withErrorHandler(async (req: NextRequest) => {
  try {
    console.log("🔍 清空所有排班记录API被调用")

    // 权限验证 - 需要员工删除权限（清空排班是危险操作，需要高级权限）
    const permissionCheck = await withEnhancedPermission(req, PERMISSIONS.EMPLOYEES_DELETE)
    if (permissionCheck) return permissionCheck

    // 额外的安全检查：需要确认参数
    const { searchParams } = new URL(req.url)
    const confirmParam = searchParams.get("confirm")

    if (confirmParam !== "true") {
      throw ErrorFactory.validation("危险操作需要确认参数：confirm=true")
    }

    // 获取当前排班记录数量
    const currentCount = await prisma.schedule.count()

    if (currentCount === 0) {
      return UnifiedErrorHandler.success(
        { deletedCount: 0 },
        "没有排班记录需要清空"
      )
    }

    // 执行清空操作
    const result = await clearAllSchedules()

    // 记录危险操作日志
    console.warn(`⚠️ 危险操作：清空了 ${currentCount} 条排班记录`)

    return UnifiedErrorHandler.success(
      {
        deletedCount: currentCount,
        operation: "clear_all_schedules",
        timestamp: new Date().toISOString()
      },
      `成功清空所有排班记录，共删除 ${currentCount} 条记录`
    )

  } catch (error) {
    console.error("清空排班记录失败:", error)
    throw error instanceof Error ? error : ErrorFactory.database("清空排班记录", error)
  }
})
