import { NextRequest, NextResponse } from "next/server"
import prisma from "@/lib/db"
import { withEnhancedPermission, PERMISSIONS } from "@/lib/auth/enhanced-auth-middleware"
import { with<PERSON><PERSON>r<PERSON><PERSON><PERSON>, UnifiedErrorHandler, ErrorFactory } from "@/lib/api/unified-error-handler"

/**
 * 获取排班记录
 */
export const GET = withErrorHandler(async (req: NextRequest) => {
  try {
    console.log("🔍 获取排班记录API被调用")

    // 权限验证 - 需要员工查看权限（排班属于员工管理）
    const permissionCheck = await withEnhancedPermission(req, PERMISSIONS.EMPLOYEES_VIEW)
    if (permissionCheck) return permissionCheck

    const { searchParams } = new URL(req.url)
    const startDate = searchParams.get("startDate")
    const endDate = searchParams.get("endDate")

    // 验证日期参数
    let startDateObj: Date | undefined
    let endDateObj: Date | undefined

    if (startDate) {
      startDateObj = new Date(startDate)
      if (isNaN(startDateObj.getTime())) {
        throw ErrorFactory.validation("开始日期格式不正确")
      }
    }

    if (endDate) {
      endDateObj = new Date(endDate)
      if (isNaN(endDateObj.getTime())) {
        throw ErrorFactory.validation("结束日期格式不正确")
      }
    }

    if (startDateObj && endDateObj && startDateObj > endDateObj) {
      throw ErrorFactory.validation("开始日期不能晚于结束日期")
    }

    let whereClause = {}

    if (startDateObj && endDateObj) {
      whereClause = {
        date: {
          gte: startDateObj,
          lte: endDateObj,
        },
      }
    }

    const schedules = await prisma.schedule.findMany({
      where: whereClause,
      include: {
        employee: {
          select: {
            id: true,
            name: true,
            position: true,
            status: true
          }
        },
      },
      orderBy: {
        date: "asc",
      },
    })

    return UnifiedErrorHandler.success({
      schedules,
      summary: {
        totalCount: schedules.length,
        dateRange: startDate && endDate ? { startDate, endDate } : null
      }
    }, `成功获取排班记录，共 ${schedules.length} 条`)

  } catch (error) {
    console.error("获取排班记录失败:", error)
    throw error instanceof Error ? error : ErrorFactory.database("获取排班记录", error)
  }
})

/**
 * 创建排班记录
 */
export const POST = withErrorHandler(async (req: NextRequest) => {
  try {
    console.log("🔍 创建排班记录API被调用")

    // 权限验证 - 需要员工更新权限（创建排班属于员工管理）
    const permissionCheck = await withEnhancedPermission(req, PERMISSIONS.EMPLOYEES_UPDATE)
    if (permissionCheck) return permissionCheck

    const data = await req.json()

    // 验证必填字段
    if (!data.employeeId) {
      throw ErrorFactory.requiredField("员工ID")
    }
    if (!data.date) {
      throw ErrorFactory.requiredField("日期")
    }
    if (!data.startTime) {
      throw ErrorFactory.requiredField("开始时间")
    }
    if (!data.endTime) {
      throw ErrorFactory.requiredField("结束时间")
    }

    // 验证员工ID
    const employeeId = Number.parseInt(data.employeeId)
    if (isNaN(employeeId) || employeeId <= 0) {
      throw ErrorFactory.validation("员工ID必须是有效的正整数")
    }

    // 验证员工是否存在
    const employee = await prisma.employee.findUnique({
      where: { id: employeeId }
    })

    if (!employee) {
      throw ErrorFactory.notFound("员工", employeeId.toString())
    }

    // 验证日期
    const scheduleDate = new Date(data.date)
    if (isNaN(scheduleDate.getTime())) {
      throw ErrorFactory.validation("日期格式不正确")
    }

    // 验证时间格式
    const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/
    if (!timeRegex.test(data.startTime)) {
      throw ErrorFactory.validation("开始时间格式不正确，应为HH:MM")
    }
    if (!timeRegex.test(data.endTime)) {
      throw ErrorFactory.validation("结束时间格式不正确，应为HH:MM")
    }

    // 验证时间逻辑
    if (data.startTime >= data.endTime) {
      throw ErrorFactory.validation("开始时间必须早于结束时间")
    }

    const schedule = await prisma.schedule.create({
      data: {
        employeeId: employeeId,
        date: scheduleDate,
        startTime: data.startTime,
        endTime: data.endTime,
      },
      include: {
        employee: {
          select: {
            id: true,
            name: true,
            position: true,
            status: true
          }
        }
      }
    })

    return UnifiedErrorHandler.success(
      schedule,
      `成功创建排班记录: ${employee.name} (${data.date} ${data.startTime}-${data.endTime})`
    )

  } catch (error) {
    console.error("创建排班记录失败:", error)
    throw error instanceof Error ? error : ErrorFactory.database("创建排班记录", error)
  }
})
