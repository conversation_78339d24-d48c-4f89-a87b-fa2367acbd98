import { NextResponse } from "next/server"
import { settingsManager } from "@/lib/settings-manager"

export async function GET() {
  try {
    // 使用统一设置管理器获取所有设置
    const settings = await settingsManager.getAllSettings()

    return NextResponse.json(settings)
  } catch (error) {
    console.error("Error fetching settings:", error)
    return NextResponse.json({ error: "Failed to fetch settings" }, { status: 500 })
  }
}

export async function PUT(request: Request) {
  try {
    const data = await request.json()

    // 使用统一设置管理器批量更新设置
    const updatedSettings = await settingsManager.updateMultipleSettings(data)

    return NextResponse.json(updatedSettings)
  } catch (error) {
    console.error("Error updating settings:", error)
    return NextResponse.json({ error: "Failed to update settings" }, { status: 500 })
  }
}
