import { NextRequest, NextResponse } from "next/server"
import { auth } from "@/auth"
import { settingsManager } from "@/lib/settings-manager"

/**
 * 统一设置API - 获取所有设置分组
 */
export async function GET(req: NextRequest) {
  try {
    // 检查认证
    const session = await auth()
    if (!session) {
      return NextResponse.json({ error: "未授权" }, { status: 401 })
    }

    const { searchParams } = new URL(req.url)
    const action = searchParams.get('action')
    
    switch (action) {
      case 'groups':
        // 获取所有设置分组
        const groups = await settingsManager.getSettingGroups()
        return NextResponse.json({
          success: true,
          data: groups,
          timestamp: new Date().toISOString()
        })

      case 'system-info':
        // 获取系统信息
        const systemInfo = await settingsManager.getSystemInfo()
        return NextResponse.json({
          success: true,
          data: systemInfo,
          timestamp: new Date().toISOString()
        })

      case 'setting':
        // 获取单个设置
        const key = searchParams.get('key')
        if (!key) {
          return NextResponse.json({ error: "缺少设置键" }, { status: 400 })
        }
        const value = await settingsManager.getSetting(key)
        return NextResponse.json({
          success: true,
          data: { key, value },
          timestamp: new Date().toISOString()
        })

      case 'settings':
        // 获取多个设置
        const keys = searchParams.get('keys')
        if (!keys) {
          return NextResponse.json({ error: "缺少设置键列表" }, { status: 400 })
        }
        const keyArray = keys.split(',')
        const values = await settingsManager.getSettings(keyArray)
        return NextResponse.json({
          success: true,
          data: values,
          timestamp: new Date().toISOString()
        })

      default:
        // 默认返回所有设置分组
        const allGroups = await settingsManager.getSettingGroups()
        return NextResponse.json({
          success: true,
          data: allGroups,
          timestamp: new Date().toISOString()
        })
    }

  } catch (error) {
    console.error("获取设置失败:", error)
    return NextResponse.json(
      { 
        error: "获取设置失败", 
        details: error instanceof Error ? error.message : "未知错误" 
      },
      { status: 500 }
    )
  }
}

/**
 * 统一设置API - 更新设置
 */
export async function PUT(req: NextRequest) {
  try {
    // 检查认证
    const session = await auth()
    if (!session) {
      return NextResponse.json({ error: "未授权" }, { status: 401 })
    }

    // TODO: 添加权限检查
    // const hasPermission = await checkPermission(session.user.id, 'settings.edit')
    // if (!hasPermission) {
    //   return NextResponse.json({ error: "权限不足" }, { status: 403 })
    // }

    const body = await req.json()
    const { action, data } = body

    switch (action) {
      case 'single':
        // 更新单个设置
        const { key, value } = data
        if (!key || value === undefined) {
          return NextResponse.json({ error: "缺少设置键或值" }, { status: 400 })
        }
        
        await settingsManager.updateSetting(key, value.toString())
        
        return NextResponse.json({
          success: true,
          message: `设置 ${key} 更新成功`,
          timestamp: new Date().toISOString()
        })

      case 'batch':
        // 批量更新设置
        const { updates } = data
        if (!updates || typeof updates !== 'object') {
          return NextResponse.json({ error: "缺少更新数据" }, { status: 400 })
        }

        await settingsManager.updateSettings(updates)

        return NextResponse.json({
          success: true,
          message: `批量更新 ${Object.keys(updates).length} 个设置成功`,
          timestamp: new Date().toISOString()
        })

      case 'reset':
        // 重置设置到默认值
        const { group } = data
        await settingsManager.resetToDefaults(group)

        return NextResponse.json({
          success: true,
          message: group ? `重置分组 ${group} 设置成功` : "重置所有系统设置成功",
          timestamp: new Date().toISOString()
        })

      default:
        return NextResponse.json({ error: "不支持的操作" }, { status: 400 })
    }

  } catch (error) {
    console.error("更新设置失败:", error)
    return NextResponse.json(
      { 
        error: "更新设置失败", 
        details: error instanceof Error ? error.message : "未知错误" 
      },
      { status: 500 }
    )
  }
}

/**
 * 统一设置API - 创建设置
 */
export async function POST(req: NextRequest) {
  try {
    // 检查认证
    const session = await auth()
    if (!session) {
      return NextResponse.json({ error: "未授权" }, { status: 401 })
    }

    // TODO: 添加权限检查
    // const hasPermission = await checkPermission(session.user.id, 'settings.create')
    // if (!hasPermission) {
    //   return NextResponse.json({ error: "权限不足" }, { status: 403 })
    // }

    const settingData = await req.json()
    
    // 验证必填字段
    const { key, value, group, type } = settingData
    if (!key || value === undefined || !group || !type) {
      return NextResponse.json({ 
        error: "缺少必填字段",
        required: ["key", "value", "group", "type"]
      }, { status: 400 })
    }

    const newSetting = await settingsManager.createSetting({
      key,
      value: value.toString(),
      description: settingData.description || '',
      group,
      type,
      options: settingData.options || '',
      isSystem: settingData.isSystem || false,
      isReadonly: settingData.isReadonly || false,
      validationRules: settingData.validationRules || '',
      defaultValue: settingData.defaultValue || ''
    })

    return NextResponse.json({
      success: true,
      message: `设置 ${key} 创建成功`,
      data: newSetting,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error("创建设置失败:", error)
    return NextResponse.json(
      { 
        error: "创建设置失败", 
        details: error instanceof Error ? error.message : "未知错误" 
      },
      { status: 500 }
    )
  }
}

/**
 * 统一设置API - 删除设置
 */
export async function DELETE(req: NextRequest) {
  try {
    // 检查认证
    const session = await auth()
    if (!session) {
      return NextResponse.json({ error: "未授权" }, { status: 401 })
    }

    // TODO: 添加权限检查
    // const hasPermission = await checkPermission(session.user.id, 'settings.delete')
    // if (!hasPermission) {
    //   return NextResponse.json({ error: "权限不足" }, { status: 403 })
    // }

    const { searchParams } = new URL(req.url)
    const key = searchParams.get('key')
    
    if (!key) {
      return NextResponse.json({ error: "缺少设置键" }, { status: 400 })
    }

    await settingsManager.deleteSetting(key)

    return NextResponse.json({
      success: true,
      message: `设置 ${key} 删除成功`,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error("删除设置失败:", error)
    return NextResponse.json(
      { 
        error: "删除设置失败", 
        details: error instanceof Error ? error.message : "未知错误" 
      },
      { status: 500 }
    )
  }
}