import { NextRequest, NextResponse } from "next/server"
import prisma from "@/lib/db"
import bcrypt from "bcryptjs"
import { withEnhancedPermission, PERMISSIONS } from "@/lib/auth/enhanced-auth-middleware"
import { RequestValidator, ValidationTemplates } from "@/lib/api/request-validator"
import { with<PERSON><PERSON>r<PERSON><PERSON><PERSON>, UnifiedErrorHandler, ErrorFactory } from "@/lib/api/unified-error-handler"
import { withRateLimit, RATE_LIMIT_CONFIGS } from "@/lib/api/rate-limiter"

// 获取所有用户
export const GET = withErrorHandler(async (req: NextRequest) => {
  try {
    // 应用速率限制 - 查询操作使用宽松限制
    const rateLimitCheck = await withRateLimit(RATE_LIMIT_CONFIGS.PERMISSIVE)(req)
    if (rateLimitCheck) return rateLimitCheck

    // 权限验证 - 需要用户查看权限
    const permissionCheck = await withEnhancedPermission(req, PERMISSIONS.USERS_VIEW)
    if (permissionCheck) return permissionCheck

    // 获取用户列表
    const users = await prisma.user.findMany({
      include: {
        employee: true,
        userRoles: {
          include: {
            role: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    })

    // 格式化用户数据
    const formattedUsers = users.map(user => ({
      id: user.id,
      name: user.name,
      email: user.email,
      emailVerified: user.emailVerified,
      image: user.image,
      role: user.role,
      employeeId: user.employeeId,
      employeeName: user.employee?.name,
      employeePosition: user.employee?.position,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      roles: user.userRoles.map(ur => ({
        id: ur.id,
        userId: ur.userId,
        roleId: ur.roleId,
        role: ur.role,
        createdAt: ur.createdAt,
        updatedAt: ur.updatedAt,
      })),
    }))

    return UnifiedErrorHandler.success(formattedUsers, "获取用户列表成功")
  } catch (error) {
    console.error("获取用户列表失败:", error)
    throw error instanceof Error ? error : ErrorFactory.internal("获取用户列表失败")
  }
})

// 创建新用户
export const POST = withErrorHandler(async (req: NextRequest) => {
  try {
    // 应用速率限制 - 创建操作使用标准限制
    const rateLimitCheck = await withRateLimit(RATE_LIMIT_CONFIGS.STANDARD)(req)
    if (rateLimitCheck) return rateLimitCheck

    // 权限验证 - 需要用户创建权限
    const permissionCheck = await withEnhancedPermission(req, PERMISSIONS.USERS_CREATE)
    if (permissionCheck) return permissionCheck

    // 数据验证
    const validation = await RequestValidator.validateRequest(req, ValidationTemplates.userCreate)
    if (!validation.isValid) {
      throw ErrorFactory.validation(
        `数据验证失败: ${validation.errors.join(', ')}`,
        undefined,
        { errors: validation.errors }
      )
    }

    const { name, email, password, employeeId, roleIds } = validation.sanitizedData

    // 检查邮箱是否已存在
    const existingUser = await prisma.user.findUnique({
      where: { email },
    })

    if (existingUser) {
      throw ErrorFactory.alreadyExists("用户", "email", email)
    }

    // 加密密码
    const hashedPassword = await bcrypt.hash(password, 10)

    // 创建用户
    const user = await prisma.user.create({
      data: {
        name,
        email,
        password: hashedPassword,
        role: "user", // 默认角色
        employeeId: employeeId || null,
      },
    })

    // 分配角色
    if (roleIds && roleIds.length > 0) {
      await prisma.userRole.createMany({
        data: roleIds.map((roleId: number) => ({
          userId: user.id,
          roleId,
        })),
        skipDuplicates: true,
      })
    }

    // 获取完整用户信息
    const createdUser = await prisma.user.findUnique({
      where: { id: user.id },
      include: {
        employee: true,
        userRoles: {
          include: {
            role: true,
          },
        },
      },
    })

    // 记录系统日志
    await prisma.systemLog.create({
      data: {
        module: "users",
        level: "info",
        message: `创建用户: ${email}`,
        details: JSON.stringify({
          userId: user.id,
          name,
          email,
          employeeId,
          roleIds,
        }),
      },
    })

    // 格式化用户数据
    const formattedUser = {
      id: createdUser?.id,
      name: createdUser?.name,
      email: createdUser?.email,
      emailVerified: createdUser?.emailVerified,
      image: createdUser?.image,
      role: createdUser?.role,
      employeeId: createdUser?.employeeId,
      employeeName: createdUser?.employee?.name,
      employeePosition: createdUser?.employee?.position,
      createdAt: createdUser?.createdAt,
      updatedAt: createdUser?.updatedAt,
      roles: createdUser?.userRoles.map(ur => ({
        id: ur.id,
        userId: ur.userId,
        roleId: ur.roleId,
        role: ur.role,
        createdAt: ur.createdAt,
        updatedAt: ur.updatedAt,
      })) || [],
    }

    return UnifiedErrorHandler.success(formattedUser, `用户 ${name} 创建成功`)
  } catch (error) {
    console.error("创建用户失败:", error)
    throw error instanceof Error ? error : ErrorFactory.internal("创建用户失败")
  }
})
