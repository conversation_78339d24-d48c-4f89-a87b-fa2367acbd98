import { NextRequest, NextResponse } from "next/server"
import prisma from "@/lib/db"
import { withEnhancedPermission, PERMISSIONS } from "@/lib/auth/enhanced-auth-middleware"
import { with<PERSON><PERSON>r<PERSON><PERSON><PERSON>, UnifiedErrorHandler, ErrorFactory } from "@/lib/api/unified-error-handler"

/**
 * 获取所有团建活动
 */
export const GET = withErrorHandler(async (req: NextRequest) => {
  try {
    console.log("🔍 获取团建活动API被调用")

    // 权限验证 - 需要员工查看权限（团建活动与员工工作相关）
    const permissionCheck = await withEnhancedPermission(req, PERMISSIONS.EMPLOYEES_VIEW)
    if (permissionCheck) return permissionCheck

    const { searchParams } = new URL(req.url)
    const isActive = searchParams.get("isActive")

    // 验证查询参数
    if (isActive && !["true", "false"].includes(isActive)) {
      throw ErrorFactory.validation("isActive参数无效，必须是 true 或 false")
    }

    let whereClause = {}
    if (isActive === "true") {
      whereClause = { isActive: true }
    } else if (isActive === "false") {
      whereClause = { isActive: false }
    }

    const activities = await prisma.workshopActivity.findMany({
      where: whereClause,
      include: {
        product: {
          select: {
            id: true,
            name: true,
            imageUrl: true,
          },
        },
      },
      orderBy: {
        id: "asc",
      },
    })

    return UnifiedErrorHandler.success({
      activities,
      summary: {
        totalCount: activities.length,
        activeCount: activities.filter(a => a.isActive).length,
        inactiveCount: activities.filter(a => !a.isActive).length,
        filters: {
          isActive: isActive || null
        }
      }
    }, `成功获取团建活动，共 ${activities.length} 个活动`)

  } catch (error) {
    console.error("获取团建活动失败:", error)
    throw error instanceof Error ? error : ErrorFactory.database("获取团建活动", error)
  }
})

/**
 * 创建团建活动
 */
export const POST = withErrorHandler(async (req: NextRequest) => {
  try {
    console.log("🔍 创建团建活动API被调用")

    // 权限验证 - 需要员工创建权限
    const permissionCheck = await withEnhancedPermission(req, PERMISSIONS.EMPLOYEES_CREATE)
    if (permissionCheck) return permissionCheck

    const data = await req.json()

    // 验证必填字段
    if (!data.name || !data.name.trim()) {
      throw ErrorFactory.requiredField("活动名称")
    }
    if (!data.productId) {
      throw ErrorFactory.requiredField("产品ID")
    }

    // 验证数值参数
    const productId = parseInt(data.productId)
    if (isNaN(productId) || productId <= 0) {
      throw ErrorFactory.validation("产品ID必须是有效的正整数")
    }

    // 验证产品是否存在
    const product = await prisma.product.findUnique({
      where: { id: productId }
    })

    if (!product) {
      throw ErrorFactory.notFound("产品", productId.toString())
    }

    // 验证数值参数范围
    const duration = data.duration ? parseInt(data.duration) : 2
    const minParticipants = data.minParticipants ? parseInt(data.minParticipants) : 5
    const maxParticipants = data.maxParticipants ? parseInt(data.maxParticipants) : 20
    const price = data.price ? parseFloat(data.price) : 0
    const materialFee = data.materialFee ? parseFloat(data.materialFee) : 0
    const teacherFee = data.teacherFee ? parseFloat(data.teacherFee) : 0
    const assistantFee = data.assistantFee ? parseFloat(data.assistantFee) : 0

    if (duration < 1 || duration > 8) {
      throw ErrorFactory.validation("活动时长必须在1-8小时之间")
    }
    if (minParticipants < 1 || minParticipants > 100) {
      throw ErrorFactory.validation("最少参与人数必须在1-100之间")
    }
    if (maxParticipants < minParticipants || maxParticipants > 100) {
      throw ErrorFactory.validation("最多参与人数必须大于等于最少人数且不超过100")
    }

    // 创建团建活动
    const activity = await prisma.workshopActivity.create({
      data: {
        name: data.name.trim(),
        description: data.description?.trim() || "",
        productId: productId,
        duration: duration,
        minParticipants: minParticipants,
        maxParticipants: maxParticipants,
        price: price,
        materialFee: materialFee,
        teacherFee: teacherFee,
        assistantFee: assistantFee,
        isActive: data.isActive !== undefined ? Boolean(data.isActive) : true,
      },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            imageUrl: true,
          },
        },
      },
    })

    return UnifiedErrorHandler.success(
      activity,
      `成功创建团建活动: ${activity.name}`
    )

  } catch (error) {
    console.error("创建团建活动失败:", error)
    throw error instanceof Error ? error : ErrorFactory.database("创建团建活动", error)
  }
})
