import { NextRequest, NextResponse } from "next/server"
import prisma from "@/lib/db"
import { withEnhancedPermission, PERMISSIONS } from "@/lib/auth/enhanced-auth-middleware"
import { with<PERSON><PERSON>r<PERSON><PERSON><PERSON>, UnifiedErrorHandler, ErrorFactory } from "@/lib/api/unified-error-handler"

/**
 * 获取所有团建服务团队成员
 */
export const GET = withErrorHandler(async (req: NextRequest) => {
  try {
    console.log("🔍 获取团建服务团队成员API被调用")

    // 权限验证 - 需要员工查看权限（团队成员属于员工管理）
    const permissionCheck = await withEnhancedPermission(req, PERMISSIONS.EMPLOYEES_VIEW)
    if (permissionCheck) return permissionCheck

    const { searchParams } = new URL(req.url)
    const role = searchParams.get("role")
    const isActive = searchParams.get("isActive")

    // 验证查询参数
    if (role && !["teacher", "assistant", "coordinator"].includes(role)) {
      throw ErrorFactory.validation("角色参数无效，必须是 teacher、assistant 或 coordinator")
    }

    if (isActive && !["true", "false"].includes(isActive)) {
      throw ErrorFactory.validation("isActive参数无效，必须是 true 或 false")
    }

    let whereClause: any = {}

    if (role) {
      whereClause.role = role
    }

    if (isActive === "true") {
      whereClause.isActive = true
    } else if (isActive === "false") {
      whereClause.isActive = false
    }

    const teamMembers = await prisma.workshopTeamMember.findMany({
      where: whereClause,
      include: {
        employee: {
          select: {
            id: true,
            name: true,
            position: true,
            phone: true,
            email: true,
            status: true,
          },
        },
      },
      orderBy: {
        id: "asc",
      },
    })

    return UnifiedErrorHandler.success({
      teamMembers,
      summary: {
        totalCount: teamMembers.length,
        filters: {
          role: role || null,
          isActive: isActive || null
        }
      }
    }, `成功获取团建服务团队成员，共 ${teamMembers.length} 个成员`)

  } catch (error) {
    console.error("获取团建服务团队成员失败:", error)
    throw error instanceof Error ? error : ErrorFactory.database("获取团建服务团队成员", error)
  }
})

/**
 * 创建团建服务团队成员
 */
export const POST = withErrorHandler(async (req: NextRequest) => {
  try {
    console.log("🔍 创建团建服务团队成员API被调用")

    // 权限验证 - 需要员工创建权限
    const permissionCheck = await withEnhancedPermission(req, PERMISSIONS.EMPLOYEES_CREATE)
    if (permissionCheck) return permissionCheck

    const data = await req.json()

    // 验证必填字段
    if (!data.employeeId) {
      throw ErrorFactory.requiredField("员工ID")
    }
    if (!data.role) {
      throw ErrorFactory.requiredField("角色")
    }

    // 验证角色值
    if (!["teacher", "assistant", "coordinator"].includes(data.role)) {
      throw ErrorFactory.validation("角色必须是 teacher、assistant 或 coordinator")
    }

    const employeeId = parseInt(data.employeeId)
    if (isNaN(employeeId) || employeeId <= 0) {
      throw ErrorFactory.validation("员工ID必须是有效的正整数")
    }

    // 检查员工是否存在
    const employee = await prisma.employee.findUnique({
      where: { id: employeeId },
    })

    if (!employee) {
      throw ErrorFactory.notFound("员工", employeeId.toString())
    }

    // 检查是否已存在相同员工和角色的记录
    const existingMember = await prisma.workshopTeamMember.findFirst({
      where: {
        employeeId: employeeId,
        role: data.role,
      },
    })

    if (existingMember) {
      throw ErrorFactory.validation(`员工 ${employee.name} 已经以 ${data.role} 角色存在于团队中`)
    }

    // 验证数值参数
    const rating = data.rating ? parseFloat(data.rating) : 5.0
    const maxWorkshopsPerDay = data.maxWorkshopsPerDay ? parseInt(data.maxWorkshopsPerDay) : 2

    if (data.rating && (isNaN(rating) || rating < 1 || rating > 10)) {
      throw ErrorFactory.validation("评分必须是1-10之间的数字")
    }

    if (data.maxWorkshopsPerDay && (isNaN(maxWorkshopsPerDay) || maxWorkshopsPerDay < 1 || maxWorkshopsPerDay > 10)) {
      throw ErrorFactory.validation("每日最大工作坊数量必须是1-10之间的整数")
    }

    // 创建团队成员
    const teamMember = await prisma.workshopTeamMember.create({
      data: {
        employeeId: employeeId,
        role: data.role,
        specialties: Array.isArray(data.specialties) ? data.specialties : [],
        rating: rating,
        maxWorkshopsPerDay: maxWorkshopsPerDay,
        isActive: data.isActive !== undefined ? Boolean(data.isActive) : true,
      },
      include: {
        employee: {
          select: {
            id: true,
            name: true,
            position: true,
            phone: true,
            email: true,
            status: true,
          },
        },
      },
    })

    return UnifiedErrorHandler.success(
      teamMember,
      `成功创建团建服务团队成员: ${employee.name} (${data.role})`
    )

  } catch (error) {
    console.error("创建团建服务团队成员失败:", error)
    throw error instanceof Error ? error : ErrorFactory.database("创建团建服务团队成员", error)
  }
})
