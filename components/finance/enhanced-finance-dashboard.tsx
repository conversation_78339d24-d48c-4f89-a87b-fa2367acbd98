"use client"

/**
 * 增强版财务仪表板组件
 * 
 * 集成高级分析、性能优化和智能提醒功能
 */

import { useState, useEffect, useMemo, useCallback } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { 
  LineChart, 
  Line, 
  AreaChart,
  Area,
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer, 
  PieChart, 
  Pie, 
  Cell, 
  BarChart, 
  Bar,
  ComposedChart 
} from 'recharts'
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  CreditCard,
  AlertTriangle,
  CheckCircle,
  Activity,
  PieChart as PieChartIcon,
  BarChart3,
  Calendar,
  Filter,
  Download,
  RefreshCw,
  Target,
  Zap,
  Shield,
  TrendingUpIcon
} from "lucide-react"
import { formatCurrency } from "@/lib/utils"

// 类型定义
interface FinancialMetrics {
  totalBalance: number;
  totalIncome: number;
  totalExpense: number;
  netProfit: number;
  profitMargin: number;
  transactionCount: number;
  activeAccounts: number;
  growthRate: number;
  burnRate: number;
  runway: number;
}

interface HealthIndicator {
  score: number;
  grade: string;
  status: string;
  recommendations: string[];
  risks: string[];
  strengths: string[];
}

interface TrendData {
  period: string;
  income: number;
  expense: number;
  profit: number;
  profitMargin: number;
  transactionCount: number;
}

interface CategoryData {
  name: string;
  value: number;
  percentage: number;
  trend: number;
  color: string;
}

interface EnhancedFinanceDashboardProps {
  timeRange?: string;
  refreshInterval?: number;
  enableRealTime?: boolean;
  showAdvancedMetrics?: boolean;
}

// 颜色主题
const CHART_COLORS = [
  '#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6',
  '#06B6D4', '#84CC16', '#F97316', '#EC4899', '#6366F1'
];

export function EnhancedFinanceDashboard({
  timeRange = '12m',
  refreshInterval = 300000, // 5分钟
  enableRealTime = true,
  showAdvancedMetrics = true
}: EnhancedFinanceDashboardProps) {
  // 状态管理
  const [loading, setLoading] = useState(true);
  const [selectedTimeRange, setSelectedTimeRange] = useState(timeRange);
  const [selectedMetric, setSelectedMetric] = useState('overview');
  const [lastUpdated, setLastUpdated] = useState(new Date());

  // 模拟数据（在实际应用中，这些数据将来自API）
  const [metrics, setMetrics] = useState<FinancialMetrics>({
    totalBalance: 235351.60,
    totalIncome: 680556.00,
    totalExpense: 155877.00,
    netProfit: 524679.00,
    profitMargin: 77.10,
    transactionCount: 366,
    activeAccounts: 4,
    growthRate: 15.8,
    burnRate: 12978.92,
    runway: 18.1
  });

  const [healthIndicator, setHealthIndicator] = useState<HealthIndicator>({
    score: 95,
    grade: 'A+',
    status: '财务状况优秀',
    recommendations: [
      '继续保持当前的盈利模式',
      '考虑扩大营销投入以提高市场份额',
      '建立应急资金储备'
    ],
    risks: [],
    strengths: [
      '高利润率表现优秀',
      '现金流充足稳定',
      '收入增长持续向好'
    ]
  });

  // 趋势数据
  const trendData: TrendData[] = useMemo(() => [
    { period: '2024-06', income: 45800, expense: 12500, profit: 33300, profitMargin: 72.7, transactionCount: 28 },
    { period: '2024-07', income: 52300, expense: 14200, profit: 38100, profitMargin: 72.8, transactionCount: 31 },
    { period: '2024-08', income: 48900, expense: 13800, profit: 35100, profitMargin: 71.8, transactionCount: 29 },
    { period: '2024-09', income: 55600, expense: 15100, profit: 40500, profitMargin: 72.8, transactionCount: 33 },
    { period: '2024-10', income: 58200, expense: 16300, profit: 41900, profitMargin: 72.0, transactionCount: 35 },
    { period: '2024-11', income: 61400, expense: 17200, profit: 44200, profitMargin: 72.0, transactionCount: 37 },
    { period: '2024-12', income: 64800, expense: 18500, profit: 46300, profitMargin: 71.4, transactionCount: 39 },
    { period: '2025-01', income: 67200, expense: 19100, profit: 48100, profitMargin: 71.6, transactionCount: 41 },
    { period: '2025-02', income: 59800, expense: 16800, profit: 43000, profitMargin: 71.9, transactionCount: 35 },
    { period: '2025-03', income: 50966, expense: 14524, profit: 36442, profitMargin: 71.5, transactionCount: 32 },
    { period: '2025-04', income: 50168, expense: 13041, profit: 37127, profitMargin: 74.0, transactionCount: 30 },
    { period: '2025-05', income: 39065, expense: 8544, profit: 30521, profitMargin: 78.1, transactionCount: 26 }
  ], []);

  // 分类数据
  const categoryData: CategoryData[] = useMemo(() => [
    { name: '珐琅产品销售', value: 265232, percentage: 39.0, trend: 12.5, color: CHART_COLORS[0] },
    { name: '工作坊教学', value: 231282, percentage: 34.0, trend: 8.3, color: CHART_COLORS[1] },
    { name: '定制服务', value: 184042, percentage: 27.0, trend: 18.7, color: CHART_COLORS[2] }
  ], []);

  // 支出分类数据
  const expenseData: CategoryData[] = useMemo(() => [
    { name: '材料采购', value: 45457, percentage: 29.2, trend: -5.2, color: CHART_COLORS[3] },
    { name: '员工薪资', value: 36407, percentage: 23.4, trend: 3.1, color: CHART_COLORS[4] },
    { name: '房租水电', value: 35816, percentage: 23.0, trend: 1.2, color: CHART_COLORS[5] },
    { name: '营销推广', value: 38197, percentage: 24.5, trend: 15.8, color: CHART_COLORS[6] }
  ], []);

  // 账户现金流数据
  const accountData = useMemo(() => [
    { name: '中国银行', balance: 185601, inflow: 280532, outflow: 94929, type: 'bank' },
    { name: '现金账户', balance: 8750, inflow: 45236, outflow: 36486, type: 'cash' },
    { name: '支付宝', balance: 25800, inflow: 195632, outflow: 169832, type: 'alipay' },
    { name: '微信支付', balance: 15201, inflow: 159156, outflow: 143955, type: 'wechat' }
  ], []);

  // 智能提醒和建议
  const smartAlerts = useMemo(() => {
    const alerts = [];
    
    // 现金流提醒
    if (metrics.runway < 3) {
      alerts.push({
        type: 'warning',
        title: '现金流预警',
        message: `当前资金储备仅能维持${metrics.runway.toFixed(1)}个月，建议增加现金储备`,
        priority: 'high'
      });
    }

    // 增长机会
    if (metrics.growthRate > 10) {
      alerts.push({
        type: 'success',
        title: '增长机会',
        message: `业务增长率达到${metrics.growthRate}%，建议考虑扩大投资`,
        priority: 'medium'
      });
    }

    // 成本优化
    const materialCostRatio = expenseData.find(e => e.name === '材料采购')?.percentage || 0;
    if (materialCostRatio > 30) {
      alerts.push({
        type: 'info',
        title: '成本优化建议',
        message: `材料成本占比较高(${materialCostRatio}%)，建议优化采购策略`,
        priority: 'low'
      });
    }

    return alerts;
  }, [metrics, expenseData]);

  // 数据刷新
  const refreshData = useCallback(async () => {
    setLoading(true);
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      setLastUpdated(new Date());
    } catch (error) {
      console.error('刷新数据失败:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  // 自动刷新
  useEffect(() => {
    if (enableRealTime && refreshInterval > 0) {
      const interval = setInterval(refreshData, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [enableRealTime, refreshInterval, refreshData]);

  // 初始化
  useEffect(() => {
    refreshData();
  }, [refreshData]);

  // 渲染健康度指示器
  const renderHealthIndicator = () => (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">财务健康度</CardTitle>
        <Shield className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="flex items-center space-x-4">
          <div className="flex-1">
            <div className="flex items-center space-x-2">
              <span className="text-2xl font-bold">{healthIndicator.score}</span>
              <Badge variant={healthIndicator.grade === 'A+' ? 'default' : 'secondary'}>
                {healthIndicator.grade}
              </Badge>
            </div>
            <p className="text-xs text-muted-foreground">{healthIndicator.status}</p>
            <Progress value={healthIndicator.score} className="mt-2" />
          </div>
          <div className="text-right">
            <div className="text-xs text-muted-foreground">评分</div>
            <div className="text-lg font-semibold">{healthIndicator.score}/100</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  // 渲染关键指标卡片
  const renderKeyMetrics = () => (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">总资产</CardTitle>
          <DollarSign className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatCurrency(metrics.totalBalance)}</div>
          <p className="text-xs text-muted-foreground">
            {metrics.activeAccounts} 个活跃账户
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">净利润</CardTitle>
          <TrendingUp className="h-4 w-4 text-green-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-green-600">
            {formatCurrency(metrics.netProfit)}
          </div>
          <p className="text-xs text-muted-foreground">
            利润率 {metrics.profitMargin.toFixed(1)}%
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">月增长率</CardTitle>
          <TrendingUpIcon className="h-4 w-4 text-blue-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-blue-600">
            {metrics.growthRate.toFixed(1)}%
          </div>
          <p className="text-xs text-muted-foreground">
            相比上月
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">资金储备</CardTitle>
          <Target className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{metrics.runway.toFixed(1)}月</div>
          <p className="text-xs text-muted-foreground">
            按当前支出计算
          </p>
        </CardContent>
      </Card>
    </div>
  );

  // 渲染智能提醒
  const renderSmartAlerts = () => (
    <div className="space-y-2">
      {smartAlerts.map((alert, index) => (
        <Alert key={index} variant={alert.type === 'warning' ? 'destructive' : 'default'}>
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>{alert.title}</AlertTitle>
          <AlertDescription>{alert.message}</AlertDescription>
        </Alert>
      ))}
    </div>
  );

  // 渲染趋势图表
  const renderTrendChart = () => (
    <Card className="col-span-2">
      <CardHeader>
        <CardTitle>财务趋势分析</CardTitle>
        <CardDescription>收入、支出和利润趋势</CardDescription>
      </CardHeader>
      <CardContent className="pl-2">
        <ResponsiveContainer width="100%" height={350}>
          <ComposedChart data={trendData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis 
              dataKey="period" 
              tickFormatter={(value) => new Date(value).toLocaleDateString('zh-CN', { month: 'short' })}
            />
            <YAxis yAxisId="left" />
            <YAxis yAxisId="right" orientation="right" />
            <Tooltip 
              formatter={(value: number, name: string) => [
                name === 'profitMargin' ? `${value.toFixed(1)}%` : formatCurrency(value),
                name === 'income' ? '收入' : 
                name === 'expense' ? '支出' : 
                name === 'profit' ? '利润' : '利润率'
              ]}
              labelFormatter={(value) => new Date(value).toLocaleDateString('zh-CN', { year: 'numeric', month: 'long' })}
            />
            <Area
              yAxisId="left"
              type="monotone"
              dataKey="income"
              stackId="1"
              stroke={CHART_COLORS[0]}
              fill={CHART_COLORS[0]}
              fillOpacity={0.6}
            />
            <Area
              yAxisId="left"
              type="monotone"
              dataKey="expense"
              stackId="2"
              stroke={CHART_COLORS[3]}
              fill={CHART_COLORS[3]}
              fillOpacity={0.6}
            />
            <Line
              yAxisId="right"
              type="monotone"
              dataKey="profitMargin"
              stroke="#FF6B6B"
              strokeWidth={3}
              dot={{ fill: "#FF6B6B", strokeWidth: 2, r: 4 }}
            />
          </ComposedChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );

  // 渲染分类分析
  const renderCategoryAnalysis = () => (
    <div className="grid gap-4 md:grid-cols-2">
      <Card>
        <CardHeader>
          <CardTitle>收入分类分析</CardTitle>
          <CardDescription>各收入来源占比</CardDescription>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={200}>
            <PieChart>
              <Pie
                data={categoryData}
                cx="50%"
                cy="50%"
                innerRadius={40}
                outerRadius={80}
                paddingAngle={5}
                dataKey="value"
              >
                {categoryData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip formatter={(value: number) => formatCurrency(value)} />
            </PieChart>
          </ResponsiveContainer>
          <div className="mt-4 space-y-2">
            {categoryData.map((category, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: category.color }}
                  />
                  <span className="text-sm">{category.name}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium">
                    {category.percentage.toFixed(1)}%
                  </span>
                  <Badge variant={category.trend > 0 ? 'default' : 'secondary'}>
                    {category.trend > 0 ? '+' : ''}{category.trend.toFixed(1)}%
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>支出分类分析</CardTitle>
          <CardDescription>各支出项目占比</CardDescription>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={200}>
            <BarChart data={expenseData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" tick={{ fontSize: 12 }} />
              <YAxis />
              <Tooltip formatter={(value: number) => formatCurrency(value)} />
              <Bar dataKey="value" fill={CHART_COLORS[6]} radius={[4, 4, 0, 0]} />
            </BarChart>
          </ResponsiveContainer>
          <div className="mt-4 space-y-2">
            {expenseData.map((expense, index) => (
              <div key={index} className="flex items-center justify-between">
                <span className="text-sm">{expense.name}</span>
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium">
                    {expense.percentage.toFixed(1)}%
                  </span>
                  <Badge variant={expense.trend < 0 ? 'default' : 'secondary'}>
                    {expense.trend > 0 ? '+' : ''}{expense.trend.toFixed(1)}%
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 顶部控制栏 */}
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <h2 className="text-2xl font-bold tracking-tight">财务仪表板</h2>
          <p className="text-muted-foreground">
            最后更新: {lastUpdated.toLocaleString('zh-CN')}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Select value={selectedTimeRange} onValueChange={setSelectedTimeRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1m">最近1月</SelectItem>
              <SelectItem value="3m">最近3月</SelectItem>
              <SelectItem value="6m">最近6月</SelectItem>
              <SelectItem value="12m">最近12月</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" size="sm" onClick={refreshData}>
            <RefreshCw className="h-4 w-4 mr-2" />
            刷新
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            导出
          </Button>
        </div>
      </div>

      {/* 智能提醒 */}
      {smartAlerts.length > 0 && (
        <div className="space-y-2">
          <h3 className="text-sm font-medium">智能提醒</h3>
          {renderSmartAlerts()}
        </div>
      )}

      {/* 关键指标 */}
      {renderKeyMetrics()}

      {/* 财务健康度 */}
      {showAdvancedMetrics && renderHealthIndicator()}

      {/* 主要图表 */}
      <Tabs value={selectedMetric} onValueChange={setSelectedMetric}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">总览</TabsTrigger>
          <TabsTrigger value="trends">趋势</TabsTrigger>
          <TabsTrigger value="analysis">分析</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-1">
            {renderTrendChart()}
          </div>
        </TabsContent>

        <TabsContent value="trends" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>月度详细趋势</CardTitle>
              <CardDescription>收入、支出、利润率详细分析</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <LineChart data={trendData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="period" 
                    tickFormatter={(value) => new Date(value).toLocaleDateString('zh-CN', { month: 'short' })}
                  />
                  <YAxis />
                  <Tooltip 
                    formatter={(value: number, name: string) => [
                      name === 'profitMargin' ? `${value.toFixed(1)}%` : formatCurrency(value),
                      name === 'income' ? '收入' : 
                      name === 'expense' ? '支出' : 
                      name === 'profit' ? '利润' : '利润率'
                    ]}
                  />
                  <Line type="monotone" dataKey="income" stroke={CHART_COLORS[0]} strokeWidth={2} />
                  <Line type="monotone" dataKey="expense" stroke={CHART_COLORS[3]} strokeWidth={2} />
                  <Line type="monotone" dataKey="profit" stroke={CHART_COLORS[1]} strokeWidth={2} />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analysis" className="space-y-4">
          {renderCategoryAnalysis()}
        </TabsContent>
      </Tabs>

      {/* 账户现金流总览 */}
      <Card>
        <CardHeader>
          <CardTitle>账户资金流向</CardTitle>
          <CardDescription>各账户资金流入流出情况</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {accountData.map((account, index) => (
              <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="flex items-center space-x-2">
                    <CreditCard className="h-5 w-5 text-muted-foreground" />
                    <span className="font-medium">{account.name}</span>
                    <Badge variant="outline">{account.type}</Badge>
                  </div>
                </div>
                <div className="flex items-center space-x-6 text-sm">
                  <div className="text-center">
                    <div className="text-green-600 font-medium">
                      ↗ {formatCurrency(account.inflow)}
                    </div>
                    <div className="text-muted-foreground">流入</div>
                  </div>
                  <div className="text-center">
                    <div className="text-red-600 font-medium">
                      ↘ {formatCurrency(account.outflow)}
                    </div>
                    <div className="text-muted-foreground">流出</div>
                  </div>
                  <div className="text-center">
                    <div className="font-bold text-lg">
                      {formatCurrency(account.balance)}
                    </div>
                    <div className="text-muted-foreground">余额</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}