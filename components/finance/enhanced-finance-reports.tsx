"use client"

/**
 * 增强版财务报表组件
 * 
 * 提供多格式报表生成、模板选择和高级配置功能
 */

import { useState, useEffect, useCallback } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { DatePickerWithRange } from "@/components/ui/date-range-picker"
import { 
  FileText,
  Download,
  Settings,
  Calendar,
  BarChart3,
  PieChart,
  TrendingUp,
  FileSpreadsheet,
  FilePdf,
  FileCSV,
  FileJson,
  Clock,
  CheckCircle,
  AlertCircle,
  Play,
  Pause,
  RefreshCw,
  Eye,
  Share2,
  Archive
} from "lucide-react"
import { DateRange } from "react-day-picker"
import { addDays, startOfMonth, endOfMonth, subMonths, format } from "date-fns"
import { zhCN } from "date-fns/locale"

// 报表配置接口
interface ReportConfig {
  title: string
  period: DateRange
  includeCharts: boolean
  includeAnalysis: boolean
  includeComparisons: boolean
  format: 'excel' | 'pdf' | 'csv' | 'json'
  templateType: 'standard' | 'executive' | 'detailed' | 'custom'
  scheduleEnabled: boolean
  scheduleFrequency?: 'daily' | 'weekly' | 'monthly' | 'quarterly'
  emailRecipients?: string[]
}

// 报表任务状态
interface ReportTask {
  id: string
  name: string
  config: ReportConfig
  status: 'pending' | 'running' | 'completed' | 'failed'
  progress: number
  createdAt: Date
  completedAt?: Date
  downloadUrl?: string
  error?: string
}

// 预定义模板
const REPORT_TEMPLATES = {
  monthly: {
    title: '月度财务报表',
    includeCharts: true,
    includeAnalysis: true,
    includeComparisons: true,
    templateType: 'standard' as const
  },
  quarterly: {
    title: '季度财务报表',
    includeCharts: true,
    includeAnalysis: true,
    includeComparisons: true,
    templateType: 'executive' as const
  },
  annual: {
    title: '年度财务报表',
    includeCharts: true,
    includeAnalysis: true,
    includeComparisons: true,
    templateType: 'detailed' as const
  },
  simple: {
    title: '简易财务报表',
    includeCharts: false,
    includeAnalysis: false,
    includeComparisons: false,
    templateType: 'standard' as const
  }
}

interface EnhancedFinanceReportsProps {
  onReportGenerated?: (task: ReportTask) => void
  onReportDownload?: (task: ReportTask) => void
}

export function EnhancedFinanceReports({
  onReportGenerated,
  onReportDownload
}: EnhancedFinanceReportsProps) {
  // 状态管理
  const [activeTab, setActiveTab] = useState('generate')
  const [reportConfig, setReportConfig] = useState<ReportConfig>({
    title: '财务报表',
    period: {
      from: startOfMonth(new Date()),
      to: endOfMonth(new Date())
    },
    includeCharts: true,
    includeAnalysis: true,
    includeComparisons: false,
    format: 'excel',
    templateType: 'standard',
    scheduleEnabled: false
  })
  const [reportTasks, setReportTasks] = useState<ReportTask[]>([])
  const [isGenerating, setIsGenerating] = useState(false)
  const [selectedTemplate, setSelectedTemplate] = useState<string>('')

  // 生成报表
  const generateReport = useCallback(async () => {
    if (!reportConfig.period.from || !reportConfig.period.to) {
      alert('请选择报表期间')
      return
    }

    setIsGenerating(true)

    const newTask: ReportTask = {
      id: `report_${Date.now()}`,
      name: reportConfig.title,
      config: reportConfig,
      status: 'pending',
      progress: 0,
      createdAt: new Date()
    }

    setReportTasks(prev => [newTask, ...prev])

    try {
      // 模拟报表生成过程
      await simulateReportGeneration(newTask.id)
      
      // 更新任务状态
      setReportTasks(prev => prev.map(task => 
        task.id === newTask.id 
          ? { 
              ...task, 
              status: 'completed' as const,
              progress: 100,
              completedAt: new Date(),
              downloadUrl: `/api/reports/download/${newTask.id}`
            }
          : task
      ))

      onReportGenerated?.(newTask)
    } catch (error) {
      // 更新失败状态
      setReportTasks(prev => prev.map(task => 
        task.id === newTask.id 
          ? { 
              ...task, 
              status: 'failed' as const,
              error: error instanceof Error ? error.message : '生成失败'
            }
          : task
      ))
    } finally {
      setIsGenerating(false)
    }
  }, [reportConfig, onReportGenerated])

  // 模拟报表生成过程
  const simulateReportGeneration = async (taskId: string) => {
    const steps = [
      { progress: 20, message: '正在获取财务数据...' },
      { progress: 40, message: '正在分析交易记录...' },
      { progress: 60, message: '正在生成图表...' },
      { progress: 80, message: '正在格式化报表...' },
      { progress: 100, message: '报表生成完成' }
    ]

    for (const step of steps) {
      await new Promise(resolve => setTimeout(resolve, 1000))
      setReportTasks(prev => prev.map(task => 
        task.id === taskId 
          ? { ...task, status: 'running' as const, progress: step.progress }
          : task
      ))
    }
  }

  // 应用模板
  const applyTemplate = useCallback((templateKey: string) => {
    const template = REPORT_TEMPLATES[templateKey as keyof typeof REPORT_TEMPLATES]
    if (template) {
      setReportConfig(prev => ({
        ...prev,
        ...template,
        period: prev.period // 保持当前选择的时间范围
      }))
      setSelectedTemplate(templateKey)
    }
  }, [])

  // 下载报表
  const downloadReport = useCallback((task: ReportTask) => {
    if (task.downloadUrl) {
      // 创建下载链接
      const link = document.createElement('a')
      link.href = task.downloadUrl
      link.download = `${task.name}_${format(task.createdAt, 'yyyyMMdd_HHmmss')}.${task.config.format}`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      onReportDownload?.(task)
    }
  }, [onReportDownload])

  // 获取格式图标
  const getFormatIcon = (format: string) => {
    switch (format) {
      case 'excel': return <FileSpreadsheet className="h-4 w-4" />
      case 'pdf': return <FilePdf className="h-4 w-4" />
      case 'csv': return <FileCSV className="h-4 w-4" />
      case 'json': return <FileJson className="h-4 w-4" />
      default: return <FileText className="h-4 w-4" />
    }
  }

  // 获取状态图标
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return <Clock className="h-4 w-4 text-gray-500" />
      case 'running': return <RefreshCw className="h-4 w-4 text-blue-500 animate-spin" />
      case 'completed': return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'failed': return <AlertCircle className="h-4 w-4 text-red-500" />
      default: return <Clock className="h-4 w-4" />
    }
  }

  // 获取状态标签
  const getStatusBadge = (status: string) => {
    const variants = {
      pending: 'secondary',
      running: 'default',
      completed: 'default',
      failed: 'destructive'
    } as const

    const labels = {
      pending: '等待中',
      running: '生成中',
      completed: '已完成',
      failed: '失败'
    }

    return (
      <Badge variant={variants[status as keyof typeof variants] || 'secondary'}>
        {labels[status as keyof typeof labels] || status}
      </Badge>
    )
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">财务报表</h2>
          <p className="text-muted-foreground">生成和管理财务报表</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <Archive className="h-4 w-4 mr-2" />
            报表归档
          </Button>
          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4 mr-2" />
            设置
          </Button>
        </div>
      </div>

      {/* 主要内容 */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="generate">生成报表</TabsTrigger>
          <TabsTrigger value="tasks">任务管理</TabsTrigger>
          <TabsTrigger value="templates">模板管理</TabsTrigger>
        </TabsList>

        {/* 生成报表标签页 */}
        <TabsContent value="generate" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            {/* 报表配置 */}
            <Card>
              <CardHeader>
                <CardTitle>报表配置</CardTitle>
                <CardDescription>设置报表的基本信息和选项</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* 报表标题 */}
                <div className="space-y-2">
                  <Label htmlFor="title">报表标题</Label>
                  <Input
                    id="title"
                    value={reportConfig.title}
                    onChange={(e) => setReportConfig(prev => ({ ...prev, title: e.target.value }))}
                    placeholder="输入报表标题"
                  />
                </div>

                {/* 报表期间 */}
                <div className="space-y-2">
                  <Label>报表期间</Label>
                  <DatePickerWithRange
                    value={reportConfig.period}
                    onChange={(range) => range && setReportConfig(prev => ({ ...prev, period: range }))}
                  />
                </div>

                {/* 报表格式 */}
                <div className="space-y-2">
                  <Label>报表格式</Label>
                  <Select 
                    value={reportConfig.format} 
                    onValueChange={(value: any) => setReportConfig(prev => ({ ...prev, format: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="excel">
                        <div className="flex items-center space-x-2">
                          <FileSpreadsheet className="h-4 w-4" />
                          <span>Excel (.xlsx)</span>
                        </div>
                      </SelectItem>
                      <SelectItem value="pdf">
                        <div className="flex items-center space-x-2">
                          <FilePdf className="h-4 w-4" />
                          <span>PDF (.pdf)</span>
                        </div>
                      </SelectItem>
                      <SelectItem value="csv">
                        <div className="flex items-center space-x-2">
                          <FileCSV className="h-4 w-4" />
                          <span>CSV (.csv)</span>
                        </div>
                      </SelectItem>
                      <SelectItem value="json">
                        <div className="flex items-center space-x-2">
                          <FileJson className="h-4 w-4" />
                          <span>JSON (.json)</span>
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* 报表选项 */}
                <div className="space-y-3">
                  <Label>报表选项</Label>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="includeCharts"
                        checked={reportConfig.includeCharts}
                        onCheckedChange={(checked) => 
                          setReportConfig(prev => ({ ...prev, includeCharts: !!checked }))
                        }
                      />
                      <Label htmlFor="includeCharts" className="text-sm">包含图表</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="includeAnalysis"
                        checked={reportConfig.includeAnalysis}
                        onCheckedChange={(checked) => 
                          setReportConfig(prev => ({ ...prev, includeAnalysis: !!checked }))
                        }
                      />
                      <Label htmlFor="includeAnalysis" className="text-sm">包含分析</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="includeComparisons"
                        checked={reportConfig.includeComparisons}
                        onCheckedChange={(checked) => 
                          setReportConfig(prev => ({ ...prev, includeComparisons: !!checked }))
                        }
                      />
                      <Label htmlFor="includeComparisons" className="text-sm">包含对比</Label>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 快速模板 */}
            <Card>
              <CardHeader>
                <CardTitle>快速模板</CardTitle>
                <CardDescription>选择预定义的报表模板</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-3">
                  {Object.entries(REPORT_TEMPLATES).map(([key, template]) => (
                    <Button
                      key={key}
                      variant={selectedTemplate === key ? "default" : "outline"}
                      className="justify-start h-auto p-4"
                      onClick={() => applyTemplate(key)}
                    >
                      <div className="text-left">
                        <div className="font-medium">{template.title}</div>
                        <div className="text-sm text-muted-foreground">
                          {template.includeCharts && '图表 '}
                          {template.includeAnalysis && '分析 '}
                          {template.includeComparisons && '对比'}
                        </div>
                      </div>
                    </Button>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 生成按钮 */}
          <div className="flex justify-center">
            <Button 
              onClick={generateReport} 
              disabled={isGenerating}
              size="lg"
              className="px-8"
            >
              {isGenerating ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  生成中...
                </>
              ) : (
                <>
                  <Play className="h-4 w-4 mr-2" />
                  生成报表
                </>
              )}
            </Button>
          </div>
        </TabsContent>

        {/* 任务管理标签页 */}
        <TabsContent value="tasks" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>报表任务</CardTitle>
              <CardDescription>查看和管理报表生成任务</CardDescription>
            </CardHeader>
            <CardContent>
              {reportTasks.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  暂无报表任务
                </div>
              ) : (
                <div className="space-y-4">
                  {reportTasks.map(task => (
                    <div key={task.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          {getStatusIcon(task.status)}
                          <div>
                            <div className="font-medium">{task.name}</div>
                            <div className="text-sm text-muted-foreground">
                              {format(task.createdAt, 'yyyy年MM月dd日 HH:mm:ss', { locale: zhCN })}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          {getFormatIcon(task.config.format)}
                          {getStatusBadge(task.status)}
                        </div>
                      </div>

                      {/* 进度条 */}
                      {task.status === 'running' && (
                        <div className="mt-3">
                          <Progress value={task.progress} className="h-2" />
                          <div className="text-xs text-muted-foreground mt-1">
                            {task.progress}% 完成
                          </div>
                        </div>
                      )}

                      {/* 错误信息 */}
                      {task.status === 'failed' && task.error && (
                        <Alert className="mt-3" variant="destructive">
                          <AlertCircle className="h-4 w-4" />
                          <AlertTitle>生成失败</AlertTitle>
                          <AlertDescription>{task.error}</AlertDescription>
                        </Alert>
                      )}

                      {/* 操作按钮 */}
                      {task.status === 'completed' && (
                        <div className="mt-3 flex items-center space-x-2">
                          <Button 
                            size="sm" 
                            variant="outline"
                            onClick={() => downloadReport(task)}
                          >
                            <Download className="h-4 w-4 mr-2" />
                            下载
                          </Button>
                          <Button size="sm" variant="outline">
                            <Eye className="h-4 w-4 mr-2" />
                            预览
                          </Button>
                          <Button size="sm" variant="outline">
                            <Share2 className="h-4 w-4 mr-2" />
                            分享
                          </Button>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* 模板管理标签页 */}
        <TabsContent value="templates" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>模板管理</CardTitle>
              <CardDescription>管理和自定义报表模板</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                模板管理功能正在开发中...
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}