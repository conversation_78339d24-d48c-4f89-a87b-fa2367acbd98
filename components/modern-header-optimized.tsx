"use client"

import { useState } from "react"
import Link from "next/link"
import { useSession, signOut } from "next-auth/react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { ModeToggle } from "@/components/mode-toggle"
import { SimpleSearch } from "@/components/ui/simple-search"
import {
  MenuIcon,
  SearchIcon,
  BellIcon,
  PlusIcon,
  UserIcon,
  SettingsIcon,
  LogOutIcon,
  PanelLeftIcon,
  PanelLeftCloseIcon,
  MoreHorizontalIcon,
} from "lucide-react"
import { cn } from "@/lib/utils"

interface ModernHeaderOptimizedProps {
  onMenuToggle?: () => void
  onQuickActionOpen?: () => void
  onSidebarToggle?: () => void
  isSidebarCollapsed?: boolean
  className?: string
}

/**
 * 优化后的现代化顶部导航栏
 * 
 * 优化重点：
 * 1. 减少认知负荷 - 精简为4个核心功能区域
 * 2. 清晰的功能层次 - 主要、次要、辅助功能分层
 * 3. 改进响应式设计 - 更好的移动端体验
 * 4. 增强可访问性 - 键盘导航和ARIA支持
 */
export function ModernHeaderOptimized({
  onMenuToggle,
  onQuickActionOpen,
  onSidebarToggle,
  isSidebarCollapsed = false,
  className
}: ModernHeaderOptimizedProps) {
  const { data: session } = useSession()
  const router = useRouter()
  const [showMobileSearch, setShowMobileSearch] = useState(false)

  // 处理退出登录
  const handleSignOut = async () => {
    try {
      await signOut({
        redirect: false,
        callbackUrl: "/login"
      })
      router.push('/login')
    } catch (error) {
      console.error('❌ 退出登录失败:', error)
      router.push('/login')
    }
  }

  // 处理移动端搜索切换
  const toggleMobileSearch = () => {
    setShowMobileSearch(!showMobileSearch)
  }

  return (
    <header 
      className={cn(
        "fixed top-0 left-0 right-0 h-16 bg-white shadow-sm flex items-center justify-between px-6 z-40 dark:bg-gray-800 dark:text-gray-100 border-b border-gray-100 dark:border-gray-700",
        className
      )}
      role="banner"
      aria-label="主导航"
    >
      {/* 移动端搜索覆盖层 */}
      {showMobileSearch && (
        <div className="absolute inset-0 bg-white dark:bg-gray-800 flex items-center px-4 md:hidden z-50">
          <SimpleSearch
            placeholder="搜索产品、订单、客户..."
            className="flex-1 mr-4"
            autoFocus
          />
          <Button
            variant="ghost"
            size="icon"
            onClick={toggleMobileSearch}
            aria-label="关闭搜索"
          >
            <SearchIcon className="w-5 h-5" />
          </Button>
        </div>
      )}

      {/* 左侧区域 - 品牌和导航控制 */}
      <div className="flex items-center min-w-0">
        {/* 移动端菜单按钮 */}
        <Button
          variant="ghost"
          size="icon"
          onClick={onMenuToggle}
          className="lg:hidden mr-2 p-2 rounded-lg"
          aria-label="打开主菜单"
        >
          <MenuIcon className="w-6 h-6" />
        </Button>

        {/* 桌面端侧边栏收缩按钮 */}
        <Button
          variant="ghost"
          size="icon"
          onClick={onSidebarToggle}
          className="hidden lg:flex mr-4 p-2 rounded-lg"
          aria-label={isSidebarCollapsed ? "展开侧边栏" : "收缩侧边栏"}
        >
          {isSidebarCollapsed ? (
            <PanelLeftIcon className="w-6 h-6" />
          ) : (
            <PanelLeftCloseIcon className="w-6 h-6" />
          )}
        </Button>

        {/* 品牌标识 */}
        <Link 
          href="/" 
          className="flex items-center text-xl font-bold text-gray-800 dark:text-gray-100 min-w-0"
          aria-label="返回首页"
        >
          <div className="w-8 h-8 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
            <span className="text-white text-sm font-bold">LH</span>
          </div>
          <span className="hidden md:block truncate">聆花掐丝珐琅馆ERP</span>
        </Link>
      </div>

      {/* 中间搜索区域 - 桌面端 */}
      <div className="flex-1 max-w-md mx-8 hidden md:block">
        <SimpleSearch
          placeholder="搜索产品、订单、客户..."
          className="w-full"
        />
      </div>

      {/* 右侧功能区域 - 精简为4个核心功能 */}
      <nav className="flex items-center space-x-2" role="navigation" aria-label="用户操作">
        {/* 移动端搜索按钮 */}
        <Button
          variant="ghost"
          size="icon"
          onClick={toggleMobileSearch}
          className="md:hidden p-2 rounded-lg"
          aria-label="搜索"
        >
          <SearchIcon className="w-5 h-5" />
        </Button>

        {/* 1. 通知中心 - 主要功能 */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="icon"
              className="relative p-2 rounded-lg"
              aria-label="通知中心"
            >
              <BellIcon className="w-5 h-5" />
              <Badge 
                variant="destructive" 
                className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs"
                aria-label="3条未读通知"
              >
                3
              </Badge>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-80">
            <DropdownMenuLabel>通知中心</DropdownMenuLabel>
            <DropdownMenuSeparator />
            {/* 通知内容 */}
            <div className="p-2 text-sm text-gray-500">暂无新通知</div>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* 2. 快速操作 - 主要功能 */}
        <Button
          variant="ghost"
          size="icon"
          onClick={onQuickActionOpen}
          className="p-2 rounded-lg"
          aria-label="快速操作"
        >
          <PlusIcon className="w-5 h-5" />
        </Button>

        {/* 3. 更多功能 - 次要功能聚合 */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="icon"
              className="p-2 rounded-lg"
              aria-label="更多功能"
            >
              <MoreHorizontalIcon className="w-5 h-5" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            <DropdownMenuLabel>更多功能</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem asChild>
              <Link href="/favorites" className="flex items-center">
                <UserIcon className="mr-2 h-4 w-4" />
                我的收藏
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <Link href="/messages" className="flex items-center">
                <UserIcon className="mr-2 h-4 w-4" />
                消息中心
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <Link href="/schedule" className="flex items-center">
                <UserIcon className="mr-2 h-4 w-4" />
                日程安排
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <Link href="/help" className="flex items-center">
                <UserIcon className="mr-2 h-4 w-4" />
                帮助中心
              </Link>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem>
              <div className="flex items-center justify-between w-full">
                <span>深色模式</span>
                <ModeToggle />
              </div>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* 4. 用户菜单 - 主要功能 */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button 
              variant="ghost" 
              className="flex items-center space-x-2 p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700"
              aria-label={`用户菜单 - ${session?.user?.name || "管理员"}`}
            >
              <Avatar className="w-8 h-8">
                <AvatarImage src={session?.user?.image || ""} alt="用户头像" />
                <AvatarFallback className="bg-indigo-500 text-white">
                  {session?.user?.name?.charAt(0) || "U"}
                </AvatarFallback>
              </Avatar>
              <span className="font-medium text-gray-800 hidden lg:block dark:text-gray-100 max-w-24 truncate">
                {session?.user?.name || "管理员"}
              </span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            <DropdownMenuLabel>我的账户</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem asChild>
              <Link href="/profile" className="flex items-center">
                <UserIcon className="mr-2 h-4 w-4" />
                个人设置
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <Link href="/settings" className="flex items-center">
                <SettingsIcon className="mr-2 h-4 w-4" />
                系统设置
              </Link>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              className="text-red-600 dark:text-red-400 cursor-pointer"
              onSelect={(event) => {
                event.preventDefault()
                handleSignOut()
              }}
            >
              <LogOutIcon className="mr-2 h-4 w-4" />
              退出登录
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </nav>
    </header>
  )
}

/**
 * 优化总结：
 * 
 * 1. 认知负荷优化：
 *    - 从9个功能区域减少到4个核心区域
 *    - 次要功能聚合到"更多功能"菜单中
 * 
 * 2. 功能层次优化：
 *    - 主要功能：通知、快速操作、用户菜单
 *    - 次要功能：收藏、消息、日程、帮助（聚合在更多菜单中）
 *    - 辅助功能：主题切换（移至更多菜单中）
 * 
 * 3. 响应式设计改进：
 *    - 移动端搜索覆盖层，保持功能完整性
 *    - 更好的文本截断处理
 *    - 合理的断点控制
 * 
 * 4. 可访问性增强：
 *    - 添加语义化HTML角色和标签
 *    - 改进ARIA标签描述
 *    - 支持键盘导航
 *    - 更好的屏幕阅读器支持
 * 
 * 5. 用户体验提升：
 *    - 清晰的视觉层次
 *    - 直观的功能分组
 *    - 流畅的交互动画
 *    - 一致的设计语言
 */