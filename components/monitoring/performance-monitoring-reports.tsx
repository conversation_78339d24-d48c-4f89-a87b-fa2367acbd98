"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { CalendarIcon, DownloadIcon, TrendingUpIcon, TrendingDownIcon, MinusIcon, FileTextIcon, BarChart3Icon, ClockIcon } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"

interface PerformanceReport {
  id: string
  title: string
  period: string
  generatedAt: string
  status: 'generating' | 'completed' | 'failed'
  metrics: {
    avgResponseTime: number
    throughput: number
    errorRate: number
    uptime: number
    cacheHitRate: number
    dbQueryTime: number
  }
  trends: {
    responseTimeTrend: number
    throughputTrend: number
    errorRateTrend: number
    cacheHitRateTrend: number
  }
  insights: string[]
  recommendations: string[]
}

interface ReportConfig {
  period: 'daily' | 'weekly' | 'monthly'
  includeMetrics: string[]
  format: 'html' | 'pdf' | 'excel'
  recipients: string[]
  autoGenerate: boolean
}

export default function PerformanceMonitoringReports() {
  const [reports, setReports] = useState<PerformanceReport[]>([])
  const [loading, setLoading] = useState(true)
  const [generateLoading, setGenerateLoading] = useState(false)
  const [selectedPeriod, setSelectedPeriod] = useState<string>('weekly')
  const [reportConfig, setReportConfig] = useState<ReportConfig>({
    period: 'weekly',
    includeMetrics: ['performance', 'cache', 'database', 'security'],
    format: 'html',
    recipients: [],
    autoGenerate: true
  })
  const { toast } = useToast()

  // 获取性能报告列表
  const fetchReports = async () => {
    try {
      const response = await fetch('/api/admin/performance-reports')
      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.message || '获取报告失败')
      }

      setReports(data.data?.reports || [])
    } catch (error) {
      console.error('获取性能报告失败:', error)
      toast({
        title: "获取报告失败",
        description: "无法加载性能报告列表",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  // 生成新报告
  const generateReport = async () => {
    setGenerateLoading(true)
    try {
      const response = await fetch('/api/admin/performance-reports', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'generate',
          period: selectedPeriod,
          config: reportConfig
        })
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.message || '生成报告失败')
      }

      toast({
        title: "报告生成成功",
        description: "性能监控报告已生成完成",
      })

      // 刷新报告列表
      await fetchReports()
    } catch (error) {
      console.error('生成报告失败:', error)
      toast({
        title: "生成报告失败",
        description: "无法生成性能报告",
        variant: "destructive"
      })
    } finally {
      setGenerateLoading(false)
    }
  }

  // 下载报告
  const downloadReport = async (reportId: string, format: string) => {
    try {
      const response = await fetch(`/api/admin/performance-reports/${reportId}/download?format=${format}`)
      
      if (!response.ok) {
        throw new Error('下载报告失败')
      }

      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `performance-report-${reportId}.${format}`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      window.URL.revokeObjectURL(url)

      toast({
        title: "下载成功",
        description: "报告已下载到本地",
      })
    } catch (error) {
      console.error('下载报告失败:', error)
      toast({
        title: "下载失败",
        description: "无法下载报告文件",
        variant: "destructive"
      })
    }
  }

  // 获取趋势图标
  const getTrendIcon = (trend: number) => {
    if (trend > 0) return <TrendingUpIcon className="h-4 w-4 text-green-600" />
    if (trend < 0) return <TrendingDownIcon className="h-4 w-4 text-red-600" />
    return <MinusIcon className="h-4 w-4 text-gray-600" />
  }

  // 获取趋势颜色
  const getTrendColor = (trend: number, isPositiveGood: boolean = true) => {
    if (trend === 0) return 'text-gray-600'
    const isGood = isPositiveGood ? trend > 0 : trend < 0
    return isGood ? 'text-green-600' : 'text-red-600'
  }

  // 格式化趋势值
  const formatTrend = (trend: number) => {
    const abs = Math.abs(trend)
    const sign = trend >= 0 ? '+' : '-'
    return `${sign}${abs.toFixed(1)}%`
  }

  useEffect(() => {
    fetchReports()
  }, [])

  return (
    <div className="space-y-6">
      {/* 顶部控制栏 */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">性能监控报告</h2>
          <p className="text-muted-foreground">自动化性能分析和趋势报告</p>
        </div>
        <div className="flex items-center space-x-4">
          <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="daily">日报</SelectItem>
              <SelectItem value="weekly">周报</SelectItem>
              <SelectItem value="monthly">月报</SelectItem>
            </SelectContent>
          </Select>
          <Button onClick={generateReport} disabled={generateLoading}>
            <FileTextIcon className="h-4 w-4 mr-2" />
            {generateLoading ? '生成中...' : '生成报告'}
          </Button>
        </div>
      </div>

      <Tabs defaultValue="reports" className="space-y-4">
        <TabsList>
          <TabsTrigger value="reports">报告列表</TabsTrigger>
          <TabsTrigger value="analytics">性能分析</TabsTrigger>
          <TabsTrigger value="config">报告配置</TabsTrigger>
        </TabsList>

        <TabsContent value="reports" className="space-y-4">
          {loading ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                <p>加载报告列表...</p>
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 gap-6">
              {reports.map((report) => (
                <Card key={report.id}>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle className="flex items-center space-x-2">
                          <span>{report.title}</span>
                          <Badge variant={report.status === 'completed' ? 'default' : 
                                       report.status === 'generating' ? 'secondary' : 'destructive'}>
                            {report.status === 'completed' ? '已完成' :
                             report.status === 'generating' ? '生成中' : '失败'}
                          </Badge>
                        </CardTitle>
                        <CardDescription>
                          {report.period} • 生成时间: {new Date(report.generatedAt).toLocaleString()}
                        </CardDescription>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => downloadReport(report.id, 'html')}
                          disabled={report.status !== 'completed'}
                        >
                          <DownloadIcon className="h-4 w-4 mr-1" />
                          HTML
                        </Button>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => downloadReport(report.id, 'pdf')}
                          disabled={report.status !== 'completed'}
                        >
                          <DownloadIcon className="h-4 w-4 mr-1" />
                          PDF
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                      <div className="text-center">
                        <div className="text-2xl font-bold">{report.metrics.avgResponseTime}ms</div>
                        <div className="text-sm text-muted-foreground flex items-center justify-center">
                          平均响应时间
                          {getTrendIcon(report.trends.responseTimeTrend)}
                          <span className={getTrendColor(report.trends.responseTimeTrend, false)}>
                            {formatTrend(report.trends.responseTimeTrend)}
                          </span>
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold">{report.metrics.throughput}</div>
                        <div className="text-sm text-muted-foreground flex items-center justify-center">
                          吞吐量 (req/min)
                          {getTrendIcon(report.trends.throughputTrend)}
                          <span className={getTrendColor(report.trends.throughputTrend)}>
                            {formatTrend(report.trends.throughputTrend)}
                          </span>
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold">{report.metrics.errorRate}%</div>
                        <div className="text-sm text-muted-foreground flex items-center justify-center">
                          错误率
                          {getTrendIcon(report.trends.errorRateTrend)}
                          <span className={getTrendColor(report.trends.errorRateTrend, false)}>
                            {formatTrend(report.trends.errorRateTrend)}
                          </span>
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold">{report.metrics.cacheHitRate}%</div>
                        <div className="text-sm text-muted-foreground flex items-center justify-center">
                          缓存命中率
                          {getTrendIcon(report.trends.cacheHitRateTrend)}
                          <span className={getTrendColor(report.trends.cacheHitRateTrend)}>
                            {formatTrend(report.trends.cacheHitRateTrend)}
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                      <div>
                        <h4 className="font-semibold mb-2">关键洞察</h4>
                        <ul className="space-y-1">
                          {report.insights.map((insight, index) => (
                            <li key={index} className="text-sm text-muted-foreground">
                              • {insight}
                            </li>
                          ))}
                        </ul>
                      </div>
                      <div>
                        <h4 className="font-semibold mb-2">优化建议</h4>
                        <ul className="space-y-1">
                          {report.recommendations.map((recommendation, index) => (
                            <li key={index} className="text-sm text-muted-foreground">
                              • {recommendation}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
              
              {reports.length === 0 && (
                <Card>
                  <CardContent className="flex items-center justify-center h-64">
                    <div className="text-center">
                      <FileTextIcon className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <h3 className="text-lg font-semibold mb-2">暂无报告</h3>
                      <p className="text-muted-foreground mb-4">
                        点击"生成报告"创建第一份性能监控报告
                      </p>
                      <Button onClick={generateReport} disabled={generateLoading}>
                        <FileTextIcon className="h-4 w-4 mr-2" />
                        生成第一份报告
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          )}
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>性能趋势分析</CardTitle>
                <CardDescription>关键性能指标的长期趋势</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span>平均响应时间</span>
                    <div className="flex items-center space-x-2">
                      <span className="font-semibold">140ms</span>
                      <TrendingDownIcon className="h-4 w-4 text-green-600" />
                      <span className="text-green-600 text-sm">-12.5%</span>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>系统吞吐量</span>
                    <div className="flex items-center space-x-2">
                      <span className="font-semibold">320 req/min</span>
                      <TrendingUpIcon className="h-4 w-4 text-green-600" />
                      <span className="text-green-600 text-sm">+18.2%</span>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>缓存命中率</span>
                    <div className="flex items-center space-x-2">
                      <span className="font-semibold">92.5%</span>
                      <TrendingUpIcon className="h-4 w-4 text-green-600" />
                      <span className="text-green-600 text-sm">+5.3%</span>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>错误率</span>
                    <div className="flex items-center space-x-2">
                      <span className="font-semibold">0.8%</span>
                      <TrendingDownIcon className="h-4 w-4 text-green-600" />
                      <span className="text-green-600 text-sm">-2.1%</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>系统健康度评分</CardTitle>
                <CardDescription>基于多维度指标的综合评估</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center mb-6">
                  <div className="text-4xl font-bold text-green-600 mb-2">94</div>
                  <div className="text-sm text-muted-foreground">健康度评分 (满分100)</div>
                </div>
                <div className="space-y-3">
                  <div>
                    <div className="flex justify-between mb-1">
                      <span className="text-sm">性能指标</span>
                      <span className="text-sm font-semibold">96/100</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-green-600 h-2 rounded-full" style={{ width: '96%' }}></div>
                    </div>
                  </div>
                  <div>
                    <div className="flex justify-between mb-1">
                      <span className="text-sm">稳定性</span>
                      <span className="text-sm font-semibold">98/100</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-green-600 h-2 rounded-full" style={{ width: '98%' }}></div>
                    </div>
                  </div>
                  <div>
                    <div className="flex justify-between mb-1">
                      <span className="text-sm">安全性</span>
                      <span className="text-sm font-semibold">92/100</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-green-600 h-2 rounded-full" style={{ width: '92%' }}></div>
                    </div>
                  </div>
                  <div>
                    <div className="flex justify-between mb-1">
                      <span className="text-sm">资源利用率</span>
                      <span className="text-sm font-semibold">90/100</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-green-600 h-2 rounded-full" style={{ width: '90%' }}></div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="config" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>报告配置</CardTitle>
              <CardDescription>自定义性能报告的生成和分发设置</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                  <label className="text-sm font-medium mb-2 block">报告周期</label>
                  <Select value={reportConfig.period} onValueChange={(value: any) => 
                    setReportConfig(prev => ({ ...prev, period: value }))
                  }>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="daily">每日</SelectItem>
                      <SelectItem value="weekly">每周</SelectItem>
                      <SelectItem value="monthly">每月</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <label className="text-sm font-medium mb-2 block">报告格式</label>
                  <Select value={reportConfig.format} onValueChange={(value: any) => 
                    setReportConfig(prev => ({ ...prev, format: value }))
                  }>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="html">HTML</SelectItem>
                      <SelectItem value="pdf">PDF</SelectItem>
                      <SelectItem value="excel">Excel</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">包含指标</label>
                <div className="grid grid-cols-2 gap-2">
                  {[
                    { id: 'performance', label: '性能指标' },
                    { id: 'cache', label: '缓存状态' },
                    { id: 'database', label: '数据库性能' },
                    { id: 'security', label: '安全监控' }
                  ].map((metric) => (
                    <label key={metric.id} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={reportConfig.includeMetrics.includes(metric.id)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setReportConfig(prev => ({
                              ...prev,
                              includeMetrics: [...prev.includeMetrics, metric.id]
                            }))
                          } else {
                            setReportConfig(prev => ({
                              ...prev,
                              includeMetrics: prev.includeMetrics.filter(m => m !== metric.id)
                            }))
                          }
                        }}
                        className="rounded"
                      />
                      <span className="text-sm">{metric.label}</span>
                    </label>
                  ))}
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="auto-generate"
                  checked={reportConfig.autoGenerate}
                  onChange={(e) => setReportConfig(prev => ({ ...prev, autoGenerate: e.target.checked }))}
                  className="rounded"
                />
                <label htmlFor="auto-generate" className="text-sm">
                  启用自动生成报告
                </label>
              </div>

              <div className="flex justify-end">
                <Button onClick={() => {
                  toast({
                    title: "配置已保存",
                    description: "报告配置更新成功",
                  })
                }}>
                  保存配置
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}