"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { 
  Shield, 
  AlertTriangle, 
  Search, 
  Download, 
  Filter, 
  CalendarIcon,
  Eye,
  Lock,
  UserX,
  Activity,
  FileText,
  TrendingUp,
  Clock
} from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import { format } from "date-fns"
import { zhCN } from "date-fns/locale"

interface SecurityEvent {
  id: string
  timestamp: string
  eventType: 'login' | 'logout' | 'permission_denied' | 'rate_limit' | 'data_access' | 'admin_action' | 'security_violation'
  severity: 'info' | 'warning' | 'error' | 'critical'
  userId?: string
  userEmail?: string
  ipAddress: string
  userAgent: string
  resource?: string
  action?: string
  description: string
  details?: Record<string, any>
  riskScore: number
}

interface SecurityStatistics {
  totalEvents: number
  threatLevel: 'low' | 'medium' | 'high'
  recentViolations: number
  blockedAttempts: number
  topRisks: Array<{
    type: string
    count: number
    trend: number
  }>
  timelineData: Array<{
    date: string
    events: number
    violations: number
  }>
}

export default function SecurityAuditLogs() {
  const [events, setEvents] = useState<SecurityEvent[]>([])
  const [statistics, setStatistics] = useState<SecurityStatistics | null>(null)
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [filterSeverity, setFilterSeverity] = useState<string>("all")
  const [filterEventType, setFilterEventType] = useState<string>("all")
  const [dateRange, setDateRange] = useState<{ from?: Date; to?: Date }>({})
  const [selectedEvent, setSelectedEvent] = useState<SecurityEvent | null>(null)
  const { toast } = useToast()

  // 获取安全审计日志
  const fetchSecurityLogs = async () => {
    try {
      const params = new URLSearchParams()
      if (searchTerm) params.append('search', searchTerm)
      if (filterSeverity !== 'all') params.append('severity', filterSeverity)
      if (filterEventType !== 'all') params.append('eventType', filterEventType)
      if (dateRange.from) params.append('startDate', dateRange.from.toISOString())
      if (dateRange.to) params.append('endDate', dateRange.to.toISOString())

      const response = await fetch(`/api/admin/security-audit?${params}`)
      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.message || '获取安全日志失败')
      }

      setEvents(data.data?.events || [])
      setStatistics(data.data?.statistics || null)
    } catch (error) {
      console.error('获取安全审计日志失败:', error)
      toast({
        title: "获取安全日志失败",
        description: "无法加载安全审计日志",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  // 导出安全日志
  const exportLogs = async (format: 'csv' | 'excel' | 'pdf') => {
    try {
      const params = new URLSearchParams()
      params.append('format', format)
      if (searchTerm) params.append('search', searchTerm)
      if (filterSeverity !== 'all') params.append('severity', filterSeverity)
      if (filterEventType !== 'all') params.append('eventType', filterEventType)
      if (dateRange.from) params.append('startDate', dateRange.from.toISOString())
      if (dateRange.to) params.append('endDate', dateRange.to.toISOString())

      const response = await fetch(`/api/admin/security-audit/export?${params}`)
      
      if (!response.ok) {
        throw new Error('导出失败')
      }

      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `security-audit-${Date.now()}.${format}`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      window.URL.revokeObjectURL(url)

      toast({
        title: "导出成功",
        description: "安全审计日志已导出",
      })
    } catch (error) {
      console.error('导出安全日志失败:', error)
      toast({
        title: "导出失败",
        description: "无法导出安全日志",
        variant: "destructive"
      })
    }
  }

  // 获取事件类型图标
  const getEventIcon = (eventType: string) => {
    switch (eventType) {
      case 'login':
        return <Shield className="h-4 w-4" />
      case 'logout':
        return <UserX className="h-4 w-4" />
      case 'permission_denied':
        return <Lock className="h-4 w-4" />
      case 'rate_limit':
        return <AlertTriangle className="h-4 w-4" />
      case 'data_access':
        return <Eye className="h-4 w-4" />
      case 'admin_action':
        return <Activity className="h-4 w-4" />
      case 'security_violation':
        return <AlertTriangle className="h-4 w-4" />
      default:
        return <FileText className="h-4 w-4" />
    }
  }

  // 获取严重程度颜色
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'error':
        return 'bg-red-50 text-red-700 border-red-200'
      case 'warning':
        return 'bg-yellow-50 text-yellow-700 border-yellow-200'
      case 'info':
        return 'bg-blue-50 text-blue-700 border-blue-200'
      default:
        return 'bg-gray-50 text-gray-700 border-gray-200'
    }
  }

  // 获取威胁等级颜色
  const getThreatLevelColor = (level: string) => {
    switch (level) {
      case 'high':
        return 'text-red-600'
      case 'medium':
        return 'text-yellow-600'
      case 'low':
        return 'text-green-600'
      default:
        return 'text-gray-600'
    }
  }

  // 格式化风险分数
  const formatRiskScore = (score: number) => {
    if (score >= 80) return { level: 'critical', color: 'text-red-600' }
    if (score >= 60) return { level: 'high', color: 'text-orange-600' }
    if (score >= 40) return { level: 'medium', color: 'text-yellow-600' }
    if (score >= 20) return { level: 'low', color: 'text-blue-600' }
    return { level: 'minimal', color: 'text-green-600' }
  }

  useEffect(() => {
    fetchSecurityLogs()
  }, [searchTerm, filterSeverity, filterEventType, dateRange])

  return (
    <div className="space-y-6">
      {/* 顶部控制栏 */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">安全审计日志</h2>
          <p className="text-muted-foreground">系统安全事件监控和审计记录</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={() => exportLogs('csv')}>
            <Download className="h-4 w-4 mr-1" />
            CSV
          </Button>
          <Button variant="outline" onClick={() => exportLogs('excel')}>
            <Download className="h-4 w-4 mr-1" />
            Excel
          </Button>
          <Button variant="outline" onClick={() => exportLogs('pdf')}>
            <Download className="h-4 w-4 mr-1" />
            PDF
          </Button>
        </div>
      </div>

      {/* 统计概览 */}
      {statistics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总事件数</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{statistics.totalEvents}</div>
              <p className="text-xs text-muted-foreground">最近24小时</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">威胁等级</CardTitle>
              <Shield className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold ${getThreatLevelColor(statistics.threatLevel)}`}>
                {statistics.threatLevel.toUpperCase()}
              </div>
              <p className="text-xs text-muted-foreground">当前安全状态</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">安全违规</CardTitle>
              <AlertTriangle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{statistics.recentViolations}</div>
              <p className="text-xs text-muted-foreground">最近发现的违规行为</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">阻止攻击</CardTitle>
              <Lock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{statistics.blockedAttempts}</div>
              <p className="text-xs text-muted-foreground">成功阻止的攻击尝试</p>
            </CardContent>
          </Card>
        </div>
      )}

      <Tabs defaultValue="events" className="space-y-4">
        <TabsList>
          <TabsTrigger value="events">安全事件</TabsTrigger>
          <TabsTrigger value="analysis">威胁分析</TabsTrigger>
          <TabsTrigger value="compliance">合规报告</TabsTrigger>
        </TabsList>

        <TabsContent value="events" className="space-y-4">
          {/* 筛选控制器 */}
          <Card>
            <CardContent className="pt-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="搜索用户、IP、描述..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
                
                <Select value={filterSeverity} onValueChange={setFilterSeverity}>
                  <SelectTrigger>
                    <SelectValue placeholder="严重程度" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部严重程度</SelectItem>
                    <SelectItem value="critical">严重</SelectItem>
                    <SelectItem value="error">错误</SelectItem>
                    <SelectItem value="warning">警告</SelectItem>
                    <SelectItem value="info">信息</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={filterEventType} onValueChange={setFilterEventType}>
                  <SelectTrigger>
                    <SelectValue placeholder="事件类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部类型</SelectItem>
                    <SelectItem value="login">登录</SelectItem>
                    <SelectItem value="logout">登出</SelectItem>
                    <SelectItem value="permission_denied">权限拒绝</SelectItem>
                    <SelectItem value="rate_limit">速率限制</SelectItem>
                    <SelectItem value="data_access">数据访问</SelectItem>
                    <SelectItem value="admin_action">管理操作</SelectItem>
                    <SelectItem value="security_violation">安全违规</SelectItem>
                  </SelectContent>
                </Select>

                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" className="justify-start">
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      开始日期
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={dateRange.from}
                      onSelect={(date) => setDateRange(prev => ({ ...prev, from: date }))}
                      locale={zhCN}
                    />
                  </PopoverContent>
                </Popover>

                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" className="justify-start">
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      结束日期
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={dateRange.to}
                      onSelect={(date) => setDateRange(prev => ({ ...prev, to: date }))}
                      locale={zhCN}
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </CardContent>
          </Card>

          {/* 事件列表 */}
          <Card>
            <CardHeader>
              <CardTitle>安全事件列表</CardTitle>
              <CardDescription>系统记录的所有安全相关事件</CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex items-center justify-center h-64">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                    <p>加载安全日志...</p>
                  </div>
                </div>
              ) : (
                <div className="space-y-2">
                  {events.map((event) => {
                    const riskInfo = formatRiskScore(event.riskScore)
                    return (
                      <div
                        key={event.id}
                        className="border rounded-lg p-4 hover:bg-gray-50 cursor-pointer transition-colors"
                        onClick={() => setSelectedEvent(event)}
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex items-start space-x-3">
                            <div className="flex-shrink-0">
                              {getEventIcon(event.eventType)}
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center space-x-2 mb-1">
                                <Badge className={getSeverityColor(event.severity)}>
                                  {event.severity}
                                </Badge>
                                <Badge variant="outline">
                                  {event.eventType.replace('_', ' ')}
                                </Badge>
                                <span className={`text-sm font-medium ${riskInfo.color}`}>
                                  风险: {event.riskScore}/100
                                </span>
                              </div>
                              <p className="text-sm font-medium text-gray-900 mb-1">
                                {event.description}
                              </p>
                              <div className="text-xs text-gray-500 space-y-1">
                                <div>用户: {event.userEmail || '未知'} | IP: {event.ipAddress}</div>
                                <div>时间: {format(new Date(event.timestamp), 'yyyy-MM-dd HH:mm:ss', { locale: zhCN })}</div>
                                {event.resource && <div>资源: {event.resource}</div>}
                              </div>
                            </div>
                          </div>
                          <div className="flex-shrink-0">
                            <Eye className="h-4 w-4 text-gray-400" />
                          </div>
                        </div>
                      </div>
                    )
                  })}
                  
                  {events.length === 0 && (
                    <div className="text-center py-12">
                      <Shield className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">无安全事件</h3>
                      <p className="text-gray-500">在当前筛选条件下未找到安全事件</p>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analysis" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>威胁趋势分析</CardTitle>
                <CardDescription>安全威胁类型分布和趋势</CardDescription>
              </CardHeader>
              <CardContent>
                {statistics?.topRisks.map((risk, index) => (
                  <div key={index} className="flex items-center justify-between py-2">
                    <span className="text-sm font-medium">{risk.type}</span>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm">{risk.count}</span>
                      <TrendingUp className={`h-4 w-4 ${risk.trend > 0 ? 'text-red-500' : 'text-green-500'}`} />
                      <span className={`text-xs ${risk.trend > 0 ? 'text-red-500' : 'text-green-500'}`}>
                        {risk.trend > 0 ? '+' : ''}{risk.trend}%
                      </span>
                    </div>
                  </div>
                )) || (
                  <p className="text-gray-500">暂无威胁趋势数据</p>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>安全指标</CardTitle>
                <CardDescription>关键安全性能指标</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span>攻击拦截率</span>
                    <span className="font-semibold text-green-600">98.5%</span>
                  </div>
                  <div className="flex justify-between">
                    <span>异常登录检测</span>
                    <span className="font-semibold">95.2%</span>
                  </div>
                  <div className="flex justify-between">
                    <span>合规性评分</span>
                    <span className="font-semibold text-blue-600">92/100</span>
                  </div>
                  <div className="flex justify-between">
                    <span>响应时间</span>
                    <span className="font-semibold">< 5分钟</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="compliance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>合规性报告</CardTitle>
              <CardDescription>系统安全合规性状态和建议</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div>
                  <h4 className="font-semibold mb-3">合规性检查项</h4>
                  <div className="space-y-2">
                    {[
                      { item: "访问控制", status: "通过", description: "用户权限管理符合要求" },
                      { item: "数据加密", status: "通过", description: "敏感数据传输和存储加密" },
                      { item: "审计日志", status: "通过", description: "完整的操作审计记录" },
                      { item: "身份验证", status: "通过", description: "多因素身份验证机制" },
                      { item: "安全监控", status: "通过", description: "实时安全事件监控" }
                    ].map((check, index) => (
                      <div key={index} className="flex items-center justify-between p-3 border rounded">
                        <div>
                          <span className="font-medium">{check.item}</span>
                          <p className="text-sm text-gray-500">{check.description}</p>
                        </div>
                        <Badge className="bg-green-100 text-green-800">
                          {check.status}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold mb-3">改进建议</h4>
                  <ul className="space-y-2">
                    <li className="text-sm">• 建议定期更新安全策略和程序文档</li>
                    <li className="text-sm">• 考虑增加更多的安全意识培训</li>
                    <li className="text-sm">• 定期进行安全渗透测试</li>
                    <li className="text-sm">• 建立应急响应预案和演练机制</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 事件详情弹窗 */}
      {selectedEvent && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>安全事件详情</CardTitle>
                <Button variant="outline" size="sm" onClick={() => setSelectedEvent(null)}>
                  关闭
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium">事件ID</label>
                    <p className="text-sm text-gray-600">{selectedEvent.id}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">时间</label>
                    <p className="text-sm text-gray-600">
                      {format(new Date(selectedEvent.timestamp), 'yyyy-MM-dd HH:mm:ss', { locale: zhCN })}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">严重程度</label>
                    <Badge className={getSeverityColor(selectedEvent.severity)}>
                      {selectedEvent.severity}
                    </Badge>
                  </div>
                  <div>
                    <label className="text-sm font-medium">风险评分</label>
                    <span className={`font-medium ${formatRiskScore(selectedEvent.riskScore).color}`}>
                      {selectedEvent.riskScore}/100
                    </span>
                  </div>
                  <div>
                    <label className="text-sm font-medium">用户</label>
                    <p className="text-sm text-gray-600">{selectedEvent.userEmail || '未知'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">IP地址</label>
                    <p className="text-sm text-gray-600">{selectedEvent.ipAddress}</p>
                  </div>
                </div>
                
                <div>
                  <label className="text-sm font-medium">事件描述</label>
                  <p className="text-sm text-gray-600 mt-1">{selectedEvent.description}</p>
                </div>
                
                <div>
                  <label className="text-sm font-medium">用户代理</label>
                  <p className="text-sm text-gray-600 break-all mt-1">{selectedEvent.userAgent}</p>
                </div>
                
                {selectedEvent.details && (
                  <div>
                    <label className="text-sm font-medium">详细信息</label>
                    <pre className="text-xs bg-gray-100 p-3 rounded mt-1 overflow-auto">
                      {JSON.stringify(selectedEvent.details, null, 2)}
                    </pre>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}