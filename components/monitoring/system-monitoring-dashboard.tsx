"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { AlertTriangle, Activity, Database, Zap, Shield, RefreshCw, TrendingUp, Server, FileText, Lock } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import PerformanceMonitoringReports from "./performance-monitoring-reports"
import SecurityAuditLogs from "./security-audit-logs"

interface SystemStats {
  performance: {
    avgResponseTime: number
    throughput: number
    errorRate: number
    uptime: number
  }
  cache: {
    hitRate: number
    totalKeys: number
    memoryUsage: number
    instances: Record<string, {
      hitRate: number
      keys: number
      memoryUsage: number
    }>
  }
  rateLimit: {
    totalRequests: number
    blockedRequests: number
    activeUsers: number
    configurations: Record<string, {
      windowMs: number
      maxRequests: number
      currentUsage: number
    }>
  }
  database: {
    queryTime: number
    connectionPool: number
    indexUsage: number
    optimizedQueries: number
  }
  security: {
    threatLevel: 'low' | 'medium' | 'high'
    blockedAttacks: number
    activeAlerts: number
    lastScan: string
  }
}

export default function SystemMonitoringDashboard() {
  const [stats, setStats] = useState<SystemStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [autoRefresh, setAutoRefresh] = useState(true)
  const [refreshInterval, setRefreshInterval] = useState(30000) // 30秒
  const { toast } = useToast()

  // 获取系统统计信息
  const fetchStats = async () => {
    try {
      const response = await fetch('/api/admin/system-monitoring')
      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.message || '获取监控数据失败')
      }

      // 直接使用统一API返回的数据
      const systemStats: SystemStats = {
        performance: data.data.performance,
        cache: data.data.cache,
        rateLimit: data.data.rateLimit,
        database: data.data.database,
        security: data.data.security
      }

      setStats(systemStats)
    } catch (error) {
      console.error('获取系统统计失败:', error)
      toast({
        title: "获取监控数据失败",
        description: "无法连接到监控服务",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  // 自动刷新
  useEffect(() => {
    fetchStats()
    
    if (autoRefresh) {
      const interval = setInterval(fetchStats, refreshInterval)
      return () => clearInterval(interval)
    }
  }, [autoRefresh, refreshInterval])

  // 手动刷新
  const handleRefresh = () => {
    setLoading(true)
    fetchStats()
  }

  // 获取状态颜色
  const getStatusColor = (value: number, thresholds: { good: number; warning: number }) => {
    if (value >= thresholds.good) return 'bg-green-500'
    if (value >= thresholds.warning) return 'bg-yellow-500'
    return 'bg-red-500'
  }

  // 格式化字节数
  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return Math.round((bytes / Math.pow(k, i)) * 100) / 100 + ' ' + sizes[i]
  }

  if (loading && !stats) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="flex items-center space-x-2">
          <RefreshCw className="h-4 w-4 animate-spin" />
          <span>加载监控数据...</span>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 顶部控制栏 */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">系统监控仪表盘</h2>
          <p className="text-muted-foreground">实时系统性能和安全监控</p>
        </div>
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <span className="text-sm">自动刷新</span>
            <Button
              variant={autoRefresh ? "default" : "outline"}
              size="sm"
              onClick={() => setAutoRefresh(!autoRefresh)}
            >
              {autoRefresh ? "开启" : "关闭"}
            </Button>
          </div>
          <Button onClick={handleRefresh} disabled={loading} size="sm">
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            刷新
          </Button>
        </div>
      </div>

      {/* 系统状态概览 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">系统性能</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">优秀</div>
            <p className="text-xs text-muted-foreground">
              响应时间: {stats?.performance.avgResponseTime}ms
            </p>
            <Progress 
              value={stats?.performance.uptime || 0} 
              className="mt-2"
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">缓存效率</CardTitle>
            <Zap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {Math.round(stats?.cache.hitRate || 0)}%
            </div>
            <p className="text-xs text-muted-foreground">
              命中率 • {stats?.cache.totalKeys} 个缓存项
            </p>
            <Progress 
              value={stats?.cache.hitRate || 0} 
              className="mt-2"
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">安全防护</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">安全</div>
            <p className="text-xs text-muted-foreground">
              已阻止 {stats?.security.blockedAttacks} 次攻击
            </p>
            <Badge variant="outline" className="mt-2">
              威胁等级: {stats?.security.threatLevel}
            </Badge>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">数据库性能</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {stats?.database.queryTime}ms
            </div>
            <p className="text-xs text-muted-foreground">
              平均查询时间 • {stats?.database.optimizedQueries} 个已优化
            </p>
            <Progress 
              value={stats?.database.indexUsage || 0} 
              className="mt-2"
            />
          </CardContent>
        </Card>
      </div>

      {/* 详细监控标签页 */}
      <Tabs defaultValue="performance" className="space-y-4">
        <TabsList>
          <TabsTrigger value="performance">性能监控</TabsTrigger>
          <TabsTrigger value="cache">缓存状态</TabsTrigger>
          <TabsTrigger value="security">安全监控</TabsTrigger>
          <TabsTrigger value="database">数据库监控</TabsTrigger>
          <TabsTrigger value="reports">性能报告</TabsTrigger>
          <TabsTrigger value="audit">安全审计</TabsTrigger>
        </TabsList>

        <TabsContent value="performance" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>实时性能指标</CardTitle>
                <CardDescription>系统响应时间和吞吐量监控</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span>平均响应时间</span>
                  <span className="font-semibold">{stats?.performance.avgResponseTime}ms</span>
                </div>
                <Progress value={Math.max(0, 100 - (stats?.performance.avgResponseTime || 200) / 5)} />
                
                <div className="flex justify-between items-center">
                  <span>请求吞吐量</span>
                  <span className="font-semibold">{stats?.performance.throughput}/min</span>
                </div>
                <Progress value={Math.min(100, (stats?.performance.throughput || 0) / 5)} />
                
                <div className="flex justify-between items-center">
                  <span>错误率</span>
                  <span className="font-semibold">{stats?.performance.errorRate}%</span>
                </div>
                <Progress value={Math.max(0, 100 - (stats?.performance.errorRate || 0) * 50)} />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>系统资源使用</CardTitle>
                <CardDescription>内存和连接池状态</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span>缓存内存使用</span>
                  <span className="font-semibold">{formatBytes(stats?.cache.memoryUsage || 0)}</span>
                </div>
                <Progress value={Math.min(100, (stats?.cache.memoryUsage || 0) / 1024 / 1024)} />
                
                <div className="flex justify-between items-center">
                  <span>数据库连接池</span>
                  <span className="font-semibold">{stats?.database.connectionPool}/50</span>
                </div>
                <Progress value={(stats?.database.connectionPool || 0) * 2} />
                
                <div className="flex justify-between items-center">
                  <span>系统正常运行时间</span>
                  <span className="font-semibold">{stats?.performance.uptime}%</span>
                </div>
                <Progress value={stats?.performance.uptime || 0} />
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="cache" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>缓存实例状态</CardTitle>
                <CardDescription>各缓存实例的详细性能指标</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {Object.entries(stats?.cache.instances || {}).map(([name, instance]) => (
                    <div key={name} className="border rounded-lg p-3">
                      <div className="flex justify-between items-center mb-2">
                        <span className="font-medium capitalize">{name}</span>
                        <Badge variant="outline">
                          {Math.round(instance.hitRate)}% 命中率
                        </Badge>
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {instance.keys} 个缓存项 • {formatBytes(instance.memoryUsage)}
                      </div>
                      <Progress value={instance.hitRate} className="mt-2" />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>缓存操作统计</CardTitle>
                <CardDescription>缓存命中率和性能趋势</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-600">
                    {Math.round(stats?.cache.hitRate || 0)}%
                  </div>
                  <div className="text-sm text-muted-foreground">总体命中率</div>
                </div>
                
                <div className="grid grid-cols-2 gap-4 text-center">
                  <div>
                    <div className="text-xl font-semibold">{stats?.cache.totalKeys}</div>
                    <div className="text-xs text-muted-foreground">缓存项数量</div>
                  </div>
                  <div>
                    <div className="text-xl font-semibold">{formatBytes(stats?.cache.memoryUsage || 0)}</div>
                    <div className="text-xs text-muted-foreground">内存使用</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="security" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>速率限制监控</CardTitle>
                <CardDescription>API访问控制和攻击防护状态</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span>总请求数</span>
                  <span className="font-semibold">{stats?.rateLimit.totalRequests}</span>
                </div>
                
                <div className="flex justify-between items-center">
                  <span>已拦截请求</span>
                  <span className="font-semibold text-red-600">{stats?.rateLimit.blockedRequests}</span>
                </div>
                
                <div className="flex justify-between items-center">
                  <span>活跃用户</span>
                  <span className="font-semibold">{stats?.rateLimit.activeUsers}</span>
                </div>
                
                <div className="pt-2">
                  <div className="text-sm text-muted-foreground mb-2">拦截率</div>
                  <Progress 
                    value={stats?.rateLimit.totalRequests ? 
                      (stats.rateLimit.blockedRequests / stats.rateLimit.totalRequests) * 100 : 0
                    } 
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>安全威胁分析</CardTitle>
                <CardDescription>系统安全状态和威胁等级</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center">
                  <Badge 
                    variant={stats?.security.threatLevel === 'low' ? 'default' : 'destructive'}
                    className="mb-2"
                  >
                    威胁等级: {stats?.security.threatLevel}
                  </Badge>
                  <div className="text-2xl font-bold text-green-600">安全</div>
                </div>
                
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>已阻止攻击</span>
                    <span className="font-semibold">{stats?.security.blockedAttacks}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>活跃警报</span>
                    <span className="font-semibold">{stats?.security.activeAlerts}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>最后扫描</span>
                    <span className="text-sm text-muted-foreground">
                      {new Date(stats?.security.lastScan || '').toLocaleTimeString()}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="database" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>数据库性能指标</CardTitle>
                <CardDescription>查询性能和优化状态</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span>平均查询时间</span>
                  <span className="font-semibold">{stats?.database.queryTime}ms</span>
                </div>
                <Progress value={Math.max(0, 100 - (stats?.database.queryTime || 100) / 2)} />
                
                <div className="flex justify-between items-center">
                  <span>索引使用率</span>
                  <span className="font-semibold">{Math.round(stats?.database.indexUsage || 0)}%</span>
                </div>
                <Progress value={stats?.database.indexUsage || 0} />
                
                <div className="flex justify-between items-center">
                  <span>已优化查询</span>
                  <span className="font-semibold">{stats?.database.optimizedQueries}</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>连接池状态</CardTitle>
                <CardDescription>数据库连接和资源使用</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-600">
                    {stats?.database.connectionPool}/50
                  </div>
                  <div className="text-sm text-muted-foreground">活跃连接</div>
                </div>
                
                <Progress value={(stats?.database.connectionPool || 0) * 2} />
                
                <div className="text-sm text-muted-foreground text-center">
                  连接池使用率: {Math.round((stats?.database.connectionPool || 0) * 2)}%
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="reports" className="space-y-4">
          <PerformanceMonitoringReports />
        </TabsContent>

        <TabsContent value="audit" className="space-y-4">
          <SecurityAuditLogs />
        </TabsContent>
      </Tabs>
    </div>
  )
}