"use client"

/**
 * 统一设置管理器组件
 * 
 * 提供完整的设置管理功能，包括查看、编辑、验证和权限控制
 */

import { useState, useEffect, useCallback } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { toast } from "@/hooks/use-toast"
import {
  Settings,
  Building,
  Shield,
  Bell,
  Database,
  Calendar,
  Save,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Info,
  Trash2,
  Plus,
  RotateCcw,
  Lock,
  Unlock
} from "lucide-react"
import { ModernPageContainer } from "@/components/modern-page-container"
import { useEnhancedOperations } from "@/lib/enhanced-operations-integration"

// 图标映射
const iconMap = {
  Settings,
  Building,
  Shield,
  Bell,
  Database,
  Calendar
}

interface SettingValue {
  id?: number
  key: string
  value: string
  description?: string
  group: string
  type: 'string' | 'number' | 'boolean' | 'json' | 'select'
  options?: string
  isSystem: boolean
  isReadonly: boolean
  validationRules?: string
  defaultValue?: string
}

interface SettingGroup {
  group: string
  title: string
  description: string
  icon: string
  order: number
  settings: SettingValue[]
}

interface SystemInfo {
  name: string
  version: string
  environment: string
  database: string
  nodeVersion: string
  memoryUsage: {
    used: number
    total: number
    percentage: number
  }
  uptime: number
}

export function UnifiedSettingsManager() {
  const enhancedOps = useEnhancedOperations()
  
  // 状态管理
  const [groups, setGroups] = useState<SettingGroup[]>([])
  const [systemInfo, setSystemInfo] = useState<SystemInfo | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [activeTab, setActiveTab] = useState("general")
  const [changedSettings, setChangedSettings] = useState<Record<string, string>>({})
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({})

  // 初始化加载
  useEffect(() => {
    Promise.all([
      loadSettingGroups(),
      loadSystemInfo()
    ]).finally(() => setLoading(false))
  }, [])

  // 加载设置分组
  const loadSettingGroups = useCallback(async () => {
    try {
      const response = await fetch("/api/settings/unified?action=groups")
      if (!response.ok) {
        throw new Error("获取设置失败")
      }
      const result = await response.json()
      if (result.success) {
        setGroups(result.data)
        // 设置默认活跃标签
        if (result.data.length > 0 && !activeTab) {
          setActiveTab(result.data[0].group)
        }
      } else {
        throw new Error(result.error || "获取设置失败")
      }
    } catch (error) {
      console.error("加载设置分组失败:", error)
      toast({
        title: "错误",
        description: "加载设置失败",
        variant: "destructive",
      })
    }
  }, [activeTab])

  // 加载系统信息
  const loadSystemInfo = useCallback(async () => {
    try {
      const response = await fetch("/api/settings/unified?action=system-info")
      if (!response.ok) {
        throw new Error("获取系统信息失败")
      }
      const result = await response.json()
      if (result.success) {
        setSystemInfo(result.data)
      }
    } catch (error) {
      console.error("加载系统信息失败:", error)
    }
  }, [])

  // 处理设置值变更
  const handleSettingChange = useCallback((key: string, value: string, setting: SettingValue) => {
    setChangedSettings(prev => ({ ...prev, [key]: value }))
    
    // 清除之前的验证错误
    setValidationErrors(prev => {
      const newErrors = { ...prev }
      delete newErrors[key]
      return newErrors
    })

    // 实时验证
    const validation = validateSetting(setting, value)
    if (!validation.valid) {
      setValidationErrors(prev => ({ ...prev, [key]: validation.error! }))
    }

    // 更新本地状态
    setGroups(prev => prev.map(group => ({
      ...group,
      settings: group.settings.map(s => 
        s.key === key ? { ...s, value } : s
      )
    })))
  }, [])

  // 设置验证
  const validateSetting = (setting: SettingValue, value: string): { valid: boolean; error?: string } => {
    try {
      switch (setting.type) {
        case 'boolean':
          if (!['true', 'false'].includes(value.toLowerCase())) {
            return { valid: false, error: '值必须为 true 或 false' }
          }
          break

        case 'number':
          const num = Number(value)
          if (isNaN(num)) {
            return { valid: false, error: '值必须为有效数字' }
          }
          if (setting.validationRules) {
            const rules = JSON.parse(setting.validationRules)
            if (rules.min !== undefined && num < rules.min) {
              return { valid: false, error: `值不能小于 ${rules.min}` }
            }
            if (rules.max !== undefined && num > rules.max) {
              return { valid: false, error: `值不能大于 ${rules.max}` }
            }
          }
          break

        case 'select':
          if (setting.options) {
            const options = setting.options.split(',').map(opt => opt.trim())
            if (!options.includes(value)) {
              return { valid: false, error: `值必须为: ${options.join(', ')}` }
            }
          }
          break

        case 'json':
          try {
            JSON.parse(value)
          } catch {
            return { valid: false, error: '值必须为有效的JSON格式' }
          }
          break
      }

      return { valid: true }
    } catch (error) {
      return { valid: false, error: '验证过程中发生错误' }
    }
  }

  // 保存所有更改
  const handleSave = async () => {
    try {
      // 检查是否有验证错误
      if (Object.keys(validationErrors).length > 0) {
        toast({
          title: "验证失败",
          description: "请修复所有验证错误后再保存",
          variant: "destructive",
        })
        return
      }

      if (Object.keys(changedSettings).length === 0) {
        toast({
          title: "提示",
          description: "没有需要保存的更改",
        })
        return
      }

      await enhancedOps.executeOperation(
        async () => {
          setSaving(true)

          const response = await fetch("/api/settings/unified", {
            method: "PUT",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              action: "batch",
              data: { updates: changedSettings }
            })
          })

          if (!response.ok) {
            const errorData = await response.json()
            throw new Error(errorData.details || errorData.error || "保存设置失败")
          }

          const result = await response.json()
          if (!result.success) {
            throw new Error(result.error || "保存设置失败")
          }

          // 清除变更记录
          setChangedSettings({})
          
          return result
        },
        {
          playSound: true,
          soundType: 'success',
          feedbackMessage: `成功保存 ${Object.keys(changedSettings).length} 个设置`,
          enableUndo: true,
          undoTags: ['settings', 'save'],
          undoPriority: 8
        }
      )

    } catch (error) {
      console.error("保存设置失败:", error)
    } finally {
      setSaving(false)
    }
  }

  // 重置到默认值
  const handleReset = async (group?: string) => {
    try {
      await enhancedOps.executeOperation(
        async () => {
          const response = await fetch("/api/settings/unified", {
            method: "PUT",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              action: "reset",
              data: { group }
            })
          })

          if (!response.ok) {
            const errorData = await response.json()
            throw new Error(errorData.details || errorData.error || "重置设置失败")
          }

          // 重新加载设置
          await loadSettingGroups()
          setChangedSettings({})
          setValidationErrors({})

          return { group }
        },
        {
          playSound: true,
          soundType: 'success',
          feedbackMessage: group ? `重置分组 ${group} 成功` : "重置所有设置成功",
          enableUndo: true,
          undoTags: ['settings', 'reset'],
          undoPriority: 9
        }
      )

    } catch (error) {
      console.error("重置设置失败:", error)
    }
  }

  // 刷新数据
  const handleRefresh = () => {
    setLoading(true)
    Promise.all([
      loadSettingGroups(),
      loadSystemInfo()
    ]).finally(() => setLoading(false))
  }

  // 渲染设置输入控件
  const renderSettingInput = (setting: SettingValue) => {
    const currentValue = changedSettings[setting.key] ?? setting.value
    const hasError = validationErrors[setting.key]
    const isChanged = changedSettings.hasOwnProperty(setting.key)

    if (setting.isReadonly) {
      return (
        <div className="relative">
          <Input
            value={currentValue}
            disabled
            className="bg-muted pr-8"
          />
          <Lock className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        </div>
      )
    }

    switch (setting.type) {
      case 'boolean':
        return (
          <div className="flex items-center space-x-2">
            <Switch
              checked={currentValue === 'true'}
              onCheckedChange={(checked) =>
                handleSettingChange(setting.key, checked ? 'true' : 'false', setting)
              }
            />
            <span className="text-sm">{currentValue === 'true' ? '启用' : '禁用'}</span>
            {isChanged && <Badge variant="outline" className="text-xs">已修改</Badge>}
          </div>
        )

      case 'number':
        return (
          <div className="space-y-1">
            <Input
              type="number"
              value={currentValue}
              onChange={(e) => handleSettingChange(setting.key, e.target.value, setting)}
              className={hasError ? "border-red-500" : ""}
            />
            {isChanged && <Badge variant="outline" className="text-xs">已修改</Badge>}
            {hasError && (
              <p className="text-sm text-red-500 flex items-center gap-1">
                <AlertCircle className="h-3 w-3" />
                {hasError}
              </p>
            )}
          </div>
        )

      case 'select':
        if (setting.options) {
          const options = setting.options.split(',').map(opt => opt.trim())
          return (
            <div className="space-y-1">
              <Select
                value={currentValue}
                onValueChange={(value) => handleSettingChange(setting.key, value, setting)}
              >
                <SelectTrigger className={hasError ? "border-red-500" : ""}>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {options.map(option => (
                    <SelectItem key={option} value={option}>
                      {option}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {isChanged && <Badge variant="outline" className="text-xs">已修改</Badge>}
            </div>
          )
        }
        // fallthrough to default

      default:
        return (
          <div className="space-y-1">
            <Input
              value={currentValue}
              onChange={(e) => handleSettingChange(setting.key, e.target.value, setting)}
              className={hasError ? "border-red-500" : ""}
            />
            {isChanged && <Badge variant="outline" className="text-xs">已修改</Badge>}
            {hasError && (
              <p className="text-sm text-red-500 flex items-center gap-1">
                <AlertCircle className="h-3 w-3" />
                {hasError}
              </p>
            )}
          </div>
        )
    }
  }

  // 获取分组图标
  const getGroupIcon = (iconName: string) => {
    const IconComponent = iconMap[iconName as keyof typeof iconMap]
    return IconComponent ? <IconComponent className="h-5 w-5" /> : <Settings className="h-5 w-5" />
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">加载设置中...</p>
        </div>
      </div>
    )
  }

  const hasChanges = Object.keys(changedSettings).length > 0
  const hasErrors = Object.keys(validationErrors).length > 0

  return (
    <ModernPageContainer
      title="系统设置管理"
      description="统一管理系统配置参数和选项"
      actions={
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={loading || saving}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? "animate-spin" : ""}`} />
            刷新
          </Button>
          {hasChanges && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setChangedSettings({})
                setValidationErrors({})
                loadSettingGroups()
              }}
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              撤销更改
            </Button>
          )}
          <Button
            onClick={handleSave}
            disabled={saving || !hasChanges || hasErrors}
            className={hasChanges ? "bg-orange-600 hover:bg-orange-700" : ""}
          >
            <Save className="mr-2 h-4 w-4" />
            {saving ? "保存中..." : hasChanges ? `保存 ${Object.keys(changedSettings).length} 项更改` : "保存"}
          </Button>
        </div>
      }
    >
      <div className="space-y-6">
        {/* 状态提示 */}
        {hasChanges && (
          <Alert className="border-orange-200 bg-orange-50">
            <Info className="h-4 w-4" />
            <AlertTitle>有未保存的更改</AlertTitle>
            <AlertDescription>
              你有 {Object.keys(changedSettings).length} 项设置未保存。请记得保存更改。
            </AlertDescription>
          </Alert>
        )}

        {hasErrors && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>验证错误</AlertTitle>
            <AlertDescription>
              有 {Object.keys(validationErrors).length} 项设置存在验证错误，请修复后再保存。
            </AlertDescription>
          </Alert>
        )}

        {/* 系统信息卡片 */}
        {systemInfo && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Info className="h-5 w-5" />
                系统信息
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                  <p className="text-sm font-medium">系统名称</p>
                  <p className="text-sm text-muted-foreground">{systemInfo.name}</p>
                </div>
                <div>
                  <p className="text-sm font-medium">版本</p>
                  <p className="text-sm text-muted-foreground">{systemInfo.version}</p>
                </div>
                <div>
                  <p className="text-sm font-medium">运行时间</p>
                  <p className="text-sm text-muted-foreground">
                    {Math.floor(systemInfo.uptime / 3600)}小时{Math.floor((systemInfo.uptime % 3600) / 60)}分钟
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium">内存使用</p>
                  <p className="text-sm text-muted-foreground">
                    {systemInfo.memoryUsage.used}MB / {systemInfo.memoryUsage.total}MB 
                    ({systemInfo.memoryUsage.percentage}%)
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* 设置标签页 */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-6">
            {groups.map(group => (
              <TabsTrigger 
                key={group.group} 
                value={group.group} 
                className="flex items-center gap-2"
              >
                {getGroupIcon(group.icon)}
                <span className="hidden sm:inline">{group.title}</span>
              </TabsTrigger>
            ))}
          </TabsList>

          {groups.map(group => (
            <TabsContent key={group.group} value={group.group}>
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="flex items-center gap-2">
                        {getGroupIcon(group.icon)}
                        {group.title}
                      </CardTitle>
                      <CardDescription>
                        {group.description}
                      </CardDescription>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleReset(group.group)}
                    >
                      <RotateCcw className="h-4 w-4 mr-2" />
                      重置此分组
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  {group.settings.length === 0 ? (
                    <p className="text-muted-foreground text-center py-8">
                      该分组暂无设置项
                    </p>
                  ) : (
                    <div className="space-y-6">
                      {group.settings.map(setting => (
                        <div key={setting.key} className="grid grid-cols-1 lg:grid-cols-3 gap-4 items-start">
                          <div className="space-y-1">
                            <Label htmlFor={setting.key} className="font-medium flex items-center gap-2">
                              {setting.description || setting.key}
                              {setting.isSystem && (
                                <Badge variant="secondary" className="text-xs">系统</Badge>
                              )}
                              {setting.isReadonly && (
                                <Badge variant="outline" className="text-xs">只读</Badge>
                              )}
                            </Label>
                            <p className="text-xs text-muted-foreground">
                              {setting.key}
                            </p>
                            {setting.defaultValue && (
                              <p className="text-xs text-muted-foreground">
                                默认值: {setting.defaultValue}
                              </p>
                            )}
                          </div>
                          <div className="lg:col-span-2">
                            {renderSettingInput(setting)}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          ))}
        </Tabs>
      </div>
    </ModernPageContainer>
  )
}