/**
 * API速率限制中间件
 * 
 * 提供灵活的速率限制功能，支持多种限制策略
 * 防止API滥用和暴力攻击
 */

import { NextRequest, NextResponse } from "next/server"
import { getToken } from "next-auth/jwt"
import { ErrorFactory } from "./unified-error-handler"
import prisma from "@/lib/db"

// 速率限制配置接口
export interface RateLimitConfig {
  windowMs: number // 时间窗口（毫秒）
  maxRequests: number // 最大请求数
  skipSuccessfulRequests?: boolean // 是否跳过成功请求
  skipFailedRequests?: boolean // 是否跳过失败请求
  keyGenerator?: (req: NextRequest) => string // 自定义key生成器
  onLimitReached?: (req: NextRequest, key: string) => void // 限制触发回调
}

// 预定义的速率限制配置
export const RATE_LIMIT_CONFIGS = {
  // 严格限制 - 用于敏感操作
  STRICT: {
    windowMs: 15 * 60 * 1000, // 15分钟
    maxRequests: 10, // 最多10次请求
  },
  
  // 认证相关 - 防止暴力破解
  AUTH: {
    windowMs: 15 * 60 * 1000, // 15分钟
    maxRequests: 5, // 最多5次请求
    skipSuccessfulRequests: true, // 成功请求不计入限制
  },
  
  // 标准限制 - 一般API操作
  STANDARD: {
    windowMs: 15 * 60 * 1000, // 15分钟
    maxRequests: 100, // 最多100次请求
  },
  
  // 宽松限制 - 查询类操作
  PERMISSIVE: {
    windowMs: 15 * 60 * 1000, // 15分钟
    maxRequests: 500, // 最多500次请求
  },
  
  // 文件上传限制
  UPLOAD: {
    windowMs: 60 * 60 * 1000, // 1小时
    maxRequests: 20, // 最多20次上传
  },
  
  // 导出操作限制
  EXPORT: {
    windowMs: 60 * 60 * 1000, // 1小时
    maxRequests: 10, // 最多10次导出
  }
} as const

/**
 * 内存存储的速率限制记录
 */
class MemoryStore {
  private store = new Map<string, { count: number; resetTime: number }>()
  
  /**
   * 清理过期记录
   */
  private cleanup() {
    const now = Date.now()
    for (const [key, record] of this.store.entries()) {
      if (now > record.resetTime) {
        this.store.delete(key)
      }
    }
  }
  
  /**
   * 增加计数
   */
  increment(key: string, windowMs: number): { count: number; resetTime: number } {
    this.cleanup()
    
    const now = Date.now()
    const resetTime = now + windowMs
    
    const record = this.store.get(key)
    
    if (!record || now > record.resetTime) {
      // 创建新记录或重置过期记录
      const newRecord = { count: 1, resetTime }
      this.store.set(key, newRecord)
      return newRecord
    } else {
      // 增加现有记录
      record.count++
      this.store.set(key, record)
      return record
    }
  }
  
  /**
   * 获取当前状态
   */
  get(key: string): { count: number; resetTime: number } | null {
    this.cleanup()
    return this.store.get(key) || null
  }
  
  /**
   * 重置指定key
   */
  reset(key: string): void {
    this.store.delete(key)
  }
  
  /**
   * 获取存储统计
   */
  getStats() {
    this.cleanup()
    return {
      totalKeys: this.store.size,
      keys: Array.from(this.store.keys())
    }
  }
}

// 全局存储实例
const store = new MemoryStore()

/**
 * 速率限制器类
 */
export class RateLimiter {
  private config: Required<RateLimitConfig>
  
  constructor(config: RateLimitConfig) {
    this.config = {
      windowMs: config.windowMs,
      maxRequests: config.maxRequests,
      skipSuccessfulRequests: config.skipSuccessfulRequests || false,
      skipFailedRequests: config.skipFailedRequests || false,
      keyGenerator: config.keyGenerator || this.defaultKeyGenerator,
      onLimitReached: config.onLimitReached || this.defaultOnLimitReached
    }
  }
  
  /**
   * 默认key生成器
   */
  private defaultKeyGenerator(req: NextRequest): string {
    // 优先使用用户ID，其次使用IP地址
    const userAgent = req.headers.get("user-agent") || "unknown"
    const ip = req.headers.get("x-forwarded-for") || 
               req.headers.get("x-real-ip") || 
               "unknown"
    
    return `${ip}:${userAgent.slice(0, 50)}`
  }
  
  /**
   * 默认限制触发回调
   */
  private defaultOnLimitReached(req: NextRequest, key: string): void {
    console.warn(`Rate limit exceeded for key: ${key}, URL: ${req.url}`)
  }
  
  /**
   * 检查请求是否超过限制
   */
  async check(req: NextRequest): Promise<{
    allowed: boolean
    limit: number
    remaining: number
    resetTime: number
    retryAfter?: number
  }> {
    const key = this.config.keyGenerator(req)
    const record = store.increment(key, this.config.windowMs)
    
    const allowed = record.count <= this.config.maxRequests
    const remaining = Math.max(0, this.config.maxRequests - record.count)
    const retryAfter = allowed ? undefined : Math.ceil((record.resetTime - Date.now()) / 1000)
    
    if (!allowed) {
      this.config.onLimitReached(req, key)
      
      // 记录速率限制日志
      try {
        const token = await getToken({ req })
        await prisma.systemLog.create({
          data: {
            module: "rate-limit",
            level: "warning",
            message: `API速率限制触发: ${req.url}`,
            details: JSON.stringify({
              key,
              count: record.count,
              limit: this.config.maxRequests,
              windowMs: this.config.windowMs,
              userAgent: req.headers.get("user-agent"),
              ip: req.headers.get("x-forwarded-for") || req.headers.get("x-real-ip"),
              userId: token?.id
            })
          }
        })
      } catch (error) {
        console.error("记录速率限制日志失败:", error)
      }
    }
    
    return {
      allowed,
      limit: this.config.maxRequests,
      remaining,
      resetTime: record.resetTime,
      retryAfter
    }
  }
}

/**
 * 用户感知的速率限制器
 * 基于用户ID而不是IP进行限制
 */
export class UserRateLimiter extends RateLimiter {
  constructor(config: RateLimitConfig) {
    super({
      ...config,
      keyGenerator: async (req: NextRequest) => {
        try {
          const token = await getToken({ req })
          if (token?.id) {
            return `user:${token.id}`
          }
        } catch (error) {
          console.error("获取用户token失败:", error)
        }
        
        // 回退到IP限制
        const ip = req.headers.get("x-forwarded-for") || 
                   req.headers.get("x-real-ip") || 
                   "unknown"
        return `ip:${ip}`
      }
    })
  }
}

/**
 * API路由级别的速率限制中间件
 */
export function withRateLimit(config: RateLimitConfig) {
  const limiter = new RateLimiter(config)
  
  return async (req: NextRequest) => {
    const result = await limiter.check(req)
    
    if (!result.allowed) {
      const response = NextResponse.json(
        {
          error: "Too Many Requests",
          type: "rate_limit",
          code: "RATE_LIMIT_EXCEEDED",
          message: "请求过于频繁，请稍后再试",
          retryAfter: result.retryAfter,
          limit: result.limit,
          remaining: result.remaining,
          resetTime: result.resetTime
        },
        { status: 429 }
      )
      
      // 添加速率限制头
      response.headers.set("X-RateLimit-Limit", result.limit.toString())
      response.headers.set("X-RateLimit-Remaining", result.remaining.toString())
      response.headers.set("X-RateLimit-Reset", result.resetTime.toString())
      if (result.retryAfter) {
        response.headers.set("Retry-After", result.retryAfter.toString())
      }
      
      throw ErrorFactory.internal("Rate limit exceeded")
    }
    
    return undefined // 继续处理请求
  }
}

/**
 * 用户级别的速率限制中间件
 */
export function withUserRateLimit(config: RateLimitConfig) {
  const limiter = new UserRateLimiter(config)
  
  return async (req: NextRequest) => {
    const result = await limiter.check(req)
    
    if (!result.allowed) {
      throw ErrorFactory.internal("User rate limit exceeded")
    }
    
    return undefined
  }
}

/**
 * 创建速率限制装饰器
 */
export function rateLimit(config: RateLimitConfig) {
  return function(target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value
    const limiter = new RateLimiter(config)
    
    descriptor.value = async function(req: NextRequest, ...args: any[]) {
      const result = await limiter.check(req)
      
      if (!result.allowed) {
        throw ErrorFactory.internal("Rate limit exceeded")
      }
      
      return originalMethod.call(this, req, ...args)
    }
    
    return descriptor
  }
}

/**
 * 获取速率限制统计信息
 */
export function getRateLimitStats() {
  return store.getStats()
}

/**
 * 重置指定key的速率限制
 */
export function resetRateLimit(key: string) {
  store.reset(key)
}