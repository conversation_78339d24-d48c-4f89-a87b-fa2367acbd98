/**
 * API请求验证中间件
 * 
 * 提供统一的请求数据验证机制
 * 防止SQL注入、XSS攻击等安全问题
 */

import { NextRequest } from "next/server"
import { ErrorFactory } from "./unified-error-handler"

// 验证规则接口
export interface ValidationRule {
  field: string
  required?: boolean
  type?: 'string' | 'number' | 'boolean' | 'email' | 'phone' | 'date' | 'array' | 'object'
  minLength?: number
  maxLength?: number
  min?: number
  max?: number
  pattern?: RegExp
  enum?: any[]
  custom?: (value: any) => boolean | string
}

// 验证结果接口
export interface ValidationResult<T = any> {
  isValid: boolean
  errors: string[]
  data?: T
  sanitizedData?: T
}

/**
 * 数据清理和安全处理
 */
export class DataSanitizer {
  /**
   * 清理字符串，防止XSS攻击
   */
  static sanitizeString(input: string): string {
    if (typeof input !== 'string') return input

    return input
      .trim()
      .replace(/[<>]/g, '') // 移除尖括号
      .replace(/javascript:/gi, '') // 移除javascript:
      .replace(/on\w+=/gi, '') // 移除事件处理器
      .replace(/eval\(/gi, '') // 移除eval
      .replace(/script/gi, '') // 移除script关键字
  }

  /**
   * 清理对象中的所有字符串字段
   */
  static sanitizeObject(obj: any): any {
    if (typeof obj !== 'object' || obj === null) {
      return obj
    }

    if (Array.isArray(obj)) {
      return obj.map(item => this.sanitizeObject(item))
    }

    const sanitized: any = {}
    for (const [key, value] of Object.entries(obj)) {
      if (typeof value === 'string') {
        sanitized[key] = this.sanitizeString(value)
      } else if (typeof value === 'object') {
        sanitized[key] = this.sanitizeObject(value)
      } else {
        sanitized[key] = value
      }
    }

    return sanitized
  }

  /**
   * 验证SQL注入模式
   */
  static checkSQLInjection(input: string): boolean {
    const sqlPatterns = [
      /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/gi,
      /(--|#|\/\*)/g,
      /(\b(OR|AND)\b.*=.*)/gi,
      /([';]+)/g
    ]

    return sqlPatterns.some(pattern => pattern.test(input))
  }

  /**
   * 验证XSS攻击模式
   */
  static checkXSS(input: string): boolean {
    const xssPatterns = [
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,
      /javascript:/gi,
      /on\w+\s*=/gi,
      /eval\s*\(/gi,
      /expression\s*\(/gi
    ]

    return xssPatterns.some(pattern => pattern.test(input))
  }
}

/**
 * 请求验证器
 */
export class RequestValidator {
  /**
   * 验证单个字段
   */
  static validateField(value: any, rule: ValidationRule): string[] {
    const errors: string[] = []
    const { field, required, type, minLength, maxLength, min, max, pattern, enum: enumValues, custom } = rule

    // 必填验证
    if (required && (value === undefined || value === null || value === '')) {
      errors.push(`${field} 为必填项`)
      return errors // 如果必填验证失败，不进行后续验证
    }

    // 如果值为空且非必填，跳过后续验证
    if (!required && (value === undefined || value === null || value === '')) {
      return errors
    }

    // 类型验证
    if (type) {
      switch (type) {
        case 'string':
          if (typeof value !== 'string') {
            errors.push(`${field} 必须为字符串`)
          } else {
            // 安全检查
            if (DataSanitizer.checkSQLInjection(value)) {
              errors.push(`${field} 包含非法字符`)
            }
            if (DataSanitizer.checkXSS(value)) {
              errors.push(`${field} 包含XSS攻击代码`)
            }
          }
          break

        case 'number':
          if (typeof value !== 'number' || isNaN(value)) {
            errors.push(`${field} 必须为有效数字`)
          }
          break

        case 'boolean':
          if (typeof value !== 'boolean') {
            errors.push(`${field} 必须为布尔值`)
          }
          break

        case 'email':
          if (typeof value === 'string') {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
            if (!emailRegex.test(value)) {
              errors.push(`${field} 必须为有效邮箱地址`)
            }
          } else {
            errors.push(`${field} 必须为字符串`)
          }
          break

        case 'phone':
          if (typeof value === 'string') {
            const phoneRegex = /^1[3-9]\d{9}$/
            if (!phoneRegex.test(value.replace(/[-\s]/g, ''))) {
              errors.push(`${field} 必须为有效手机号码`)
            }
          } else {
            errors.push(`${field} 必须为字符串`)
          }
          break

        case 'date':
          const date = new Date(value)
          if (isNaN(date.getTime())) {
            errors.push(`${field} 必须为有效日期`)
          }
          break

        case 'array':
          if (!Array.isArray(value)) {
            errors.push(`${field} 必须为数组`)
          }
          break

        case 'object':
          if (typeof value !== 'object' || Array.isArray(value) || value === null) {
            errors.push(`${field} 必须为对象`)
          }
          break
      }
    }

    // 长度验证
    if (typeof value === 'string') {
      if (minLength !== undefined && value.length < minLength) {
        errors.push(`${field} 长度不能少于 ${minLength} 个字符`)
      }
      if (maxLength !== undefined && value.length > maxLength) {
        errors.push(`${field} 长度不能超过 ${maxLength} 个字符`)
      }
    }

    // 数值范围验证
    if (typeof value === 'number') {
      if (min !== undefined && value < min) {
        errors.push(`${field} 不能小于 ${min}`)
      }
      if (max !== undefined && value > max) {
        errors.push(`${field} 不能大于 ${max}`)
      }
    }

    // 正则表达式验证
    if (pattern && typeof value === 'string') {
      if (!pattern.test(value)) {
        errors.push(`${field} 格式不正确`)
      }
    }

    // 枚举值验证
    if (enumValues && !enumValues.includes(value)) {
      errors.push(`${field} 必须为以下值之一: ${enumValues.join(', ')}`)
    }

    // 自定义验证
    if (custom) {
      const result = custom(value)
      if (typeof result === 'string') {
        errors.push(result)
      } else if (result === false) {
        errors.push(`${field} 验证失败`)
      }
    }

    return errors
  }

  /**
   * 验证整个请求数据
   */
  static validate<T>(data: any, rules: ValidationRule[]): ValidationResult<T> {
    const errors: string[] = []
    const sanitizedData: any = DataSanitizer.sanitizeObject(data)

    for (const rule of rules) {
      const fieldErrors = this.validateField(sanitizedData[rule.field], rule)
      errors.push(...fieldErrors)
    }

    return {
      isValid: errors.length === 0,
      errors,
      data: sanitizedData,
      sanitizedData
    }
  }

  /**
   * 从请求中获取并验证数据
   */
  static async validateRequest<T>(
    req: NextRequest, 
    rules: ValidationRule[]
  ): Promise<ValidationResult<T>> {
    try {
      const body = await req.json()
      return this.validate<T>(body, rules)
    } catch (error) {
      throw ErrorFactory.validation("请求数据格式无效")
    }
  }

  /**
   * 验证查询参数
   */
  static validateQuery(
    req: NextRequest,
    rules: ValidationRule[]
  ): ValidationResult {
    const query: any = {}
    const searchParams = req.nextUrl.searchParams

    // 提取查询参数
    for (const rule of rules) {
      const value = searchParams.get(rule.field)
      if (value !== null) {
        // 尝试类型转换
        if (rule.type === 'number') {
          const num = Number(value)
          query[rule.field] = isNaN(num) ? value : num
        } else if (rule.type === 'boolean') {
          query[rule.field] = value === 'true' || value === '1'
        } else {
          query[rule.field] = value
        }
      }
    }

    return this.validate(query, rules)
  }
}

/**
 * 常用验证规则模板
 */
export const ValidationTemplates = {
  // 用户相关
  userCreate: [
    { field: 'name', required: true, type: 'string' as const, minLength: 1, maxLength: 50 },
    { field: 'email', required: true, type: 'email' as const },
    { field: 'password', required: true, type: 'string' as const, minLength: 6, maxLength: 100 },
    { field: 'phone', required: false, type: 'phone' as const },
    { field: 'employeeId', required: false, type: 'number' as const }
  ],

  userUpdate: [
    { field: 'name', required: false, type: 'string' as const, minLength: 1, maxLength: 50 },
    { field: 'email', required: false, type: 'email' as const },
    { field: 'phone', required: false, type: 'phone' as const },
    { field: 'employeeId', required: false, type: 'number' as const }
  ],

  // 角色相关
  roleCreate: [
    { field: 'name', required: true, type: 'string' as const, minLength: 1, maxLength: 50 },
    { field: 'code', required: true, type: 'string' as const, pattern: /^[A-Z][A-Z0-9_]*$/ },
    { field: 'description', required: false, type: 'string' as const, maxLength: 200 },
    { field: 'permissionIds', required: false, type: 'array' as const }
  ],

  // 员工相关
  employeeCreate: [
    { field: 'name', required: true, type: 'string' as const, minLength: 1, maxLength: 50 },
    { field: 'position', required: false, type: 'string' as const, maxLength: 50 },
    { field: 'phone', required: false, type: 'phone' as const },
    { field: 'email', required: false, type: 'email' as const },
    { field: 'department', required: false, type: 'string' as const, maxLength: 50 }
  ],

  // 分页查询
  pagination: [
    { field: 'page', required: false, type: 'number' as const, min: 1 },
    { field: 'limit', required: false, type: 'number' as const, min: 1, max: 100 },
    { field: 'search', required: false, type: 'string' as const, maxLength: 100 },
    { field: 'sortBy', required: false, type: 'string' as const },
    { field: 'sortOrder', required: false, type: 'string' as const, enum: ['asc', 'desc'] }
  ],

  // ID参数验证
  idParam: [
    { field: 'id', required: true, type: 'number' as const, min: 1 }
  ]
}

/**
 * 验证中间件装饰器
 */
export function validateBody(rules: ValidationRule[]) {
  return function(target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value

    descriptor.value = async function(req: NextRequest, ...args: any[]) {
      const validation = await RequestValidator.validateRequest(req, rules)
      
      if (!validation.isValid) {
        throw ErrorFactory.validation(
          `请求数据验证失败: ${validation.errors.join(', ')}`,
          undefined,
          { errors: validation.errors }
        )
      }

      // 将验证后的数据替换原始请求
      return originalMethod.call(this, req, validation.sanitizedData, ...args)
    }

    return descriptor
  }
}

/**
 * 验证查询参数装饰器
 */
export function validateQuery(rules: ValidationRule[]) {
  return function(target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value

    descriptor.value = async function(req: NextRequest, ...args: any[]) {
      const validation = RequestValidator.validateQuery(req, rules)
      
      if (!validation.isValid) {
        throw ErrorFactory.validation(
          `查询参数验证失败: ${validation.errors.join(', ')}`,
          undefined,
          { errors: validation.errors }
        )
      }

      return originalMethod.call(this, req, validation.sanitizedData, ...args)
    }

    return descriptor
  }
}