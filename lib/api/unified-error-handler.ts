/**
 * 统一API错误处理中间件
 * 
 * 提供标准化的错误响应格式和处理机制
 * 确保所有API端点的错误处理一致性
 */

import { NextRequest, NextResponse } from "next/server"
import prisma from "@/lib/db"

// 错误类型枚举
export enum ErrorType {
  VALIDATION = "validation",
  AUTHENTICATION = "authentication", 
  AUTHORIZATION = "authorization",
  NOT_FOUND = "not_found",
  CONFLICT = "conflict",
  INTERNAL = "internal",
  BAD_REQUEST = "bad_request",
  RATE_LIMIT = "rate_limit"
}

// 标准错误响应接口
export interface ApiError {
  error: string
  type: ErrorType
  code: string
  message: string
  details?: any
  timestamp: string
  requestId?: string
  field?: string
  statusCode: number
}

// 错误代码映射
export const ERROR_CODES = {
  // 认证相关 (1000-1999)
  INVALID_TOKEN: "AUTH_1001",
  TOKEN_EXPIRED: "AUTH_1002",
  UNAUTHORIZED: "AUTH_1003",
  FORBIDDEN: "AUTH_1004",
  INVALID_CREDENTIALS: "AUTH_1005",
  
  // 验证相关 (2000-2999)
  VALIDATION_FAILED: "VAL_2001",
  REQUIRED_FIELD: "VAL_2002",
  INVALID_FORMAT: "VAL_2003",
  OUT_OF_RANGE: "VAL_2004",
  DUPLICATE_VALUE: "VAL_2005",
  
  // 资源相关 (3000-3999)
  NOT_FOUND: "RES_3001",
  ALREADY_EXISTS: "RES_3002",
  DEPENDENCY_VIOLATION: "RES_3003",
  RESOURCE_LOCKED: "RES_3004",
  
  // 业务逻辑相关 (4000-4999)
  BUSINESS_RULE_VIOLATION: "BIZ_4001",
  OPERATION_NOT_ALLOWED: "BIZ_4002",
  INSUFFICIENT_PERMISSIONS: "BIZ_4003",
  QUOTA_EXCEEDED: "BIZ_4004",
  
  // 系统相关 (5000-5999)
  DATABASE_ERROR: "SYS_5001",
  EXTERNAL_SERVICE_ERROR: "SYS_5002",
  INTERNAL_SERVER_ERROR: "SYS_5003",
  SERVICE_UNAVAILABLE: "SYS_5004",
  TIMEOUT: "SYS_5005"
} as const

// HTTP状态码映射
const HTTP_STATUS_MAP: Record<ErrorType, number> = {
  [ErrorType.VALIDATION]: 400,
  [ErrorType.AUTHENTICATION]: 401,
  [ErrorType.AUTHORIZATION]: 403,
  [ErrorType.NOT_FOUND]: 404,
  [ErrorType.CONFLICT]: 409,
  [ErrorType.BAD_REQUEST]: 400,
  [ErrorType.RATE_LIMIT]: 429,
  [ErrorType.INTERNAL]: 500
}

// 预定义错误类
export class AppError extends Error {
  public readonly type: ErrorType
  public readonly code: string
  public readonly statusCode: number
  public readonly details?: any
  public readonly field?: string

  constructor(
    type: ErrorType,
    code: string,
    message: string,
    details?: any,
    field?: string
  ) {
    super(message)
    this.type = type
    this.code = code
    this.statusCode = HTTP_STATUS_MAP[type]
    this.details = details
    this.field = field
    
    // 确保错误名称正确
    this.name = 'AppError'
    
    // 如果支持，设置正确的堆栈跟踪
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, AppError)
    }
  }
}

// 快捷错误创建方法
export class ErrorFactory {
  static validation(message: string, field?: string, details?: any): AppError {
    return new AppError(
      ErrorType.VALIDATION,
      ERROR_CODES.VALIDATION_FAILED,
      message,
      details,
      field
    )
  }

  static requiredField(field: string): AppError {
    return new AppError(
      ErrorType.VALIDATION,
      ERROR_CODES.REQUIRED_FIELD,
      `字段 ${field} 为必填项`,
      null,
      field
    )
  }

  static invalidFormat(field: string, expectedFormat?: string): AppError {
    const message = expectedFormat 
      ? `字段 ${field} 格式无效，期望格式: ${expectedFormat}`
      : `字段 ${field} 格式无效`
    
    return new AppError(
      ErrorType.VALIDATION,
      ERROR_CODES.INVALID_FORMAT,
      message,
      { expectedFormat },
      field
    )
  }

  static notFound(resource: string, id?: string | number): AppError {
    const message = id ? `${resource} (ID: ${id}) 不存在` : `${resource} 不存在`
    return new AppError(
      ErrorType.NOT_FOUND,
      ERROR_CODES.NOT_FOUND,
      message,
      { resource, id }
    )
  }

  static alreadyExists(resource: string, field?: string, value?: any): AppError {
    const message = field 
      ? `${resource} 的 ${field} 值 ${value} 已存在`
      : `${resource} 已存在`
    
    return new AppError(
      ErrorType.CONFLICT,
      ERROR_CODES.ALREADY_EXISTS,
      message,
      { resource, field, value }
    )
  }

  static unauthorized(message: string = "未授权访问"): AppError {
    return new AppError(
      ErrorType.AUTHENTICATION,
      ERROR_CODES.UNAUTHORIZED,
      message
    )
  }

  static forbidden(permission?: string): AppError {
    const message = permission 
      ? `没有权限执行此操作，需要权限: ${permission}`
      : "没有权限执行此操作"
    
    return new AppError(
      ErrorType.AUTHORIZATION,
      ERROR_CODES.FORBIDDEN,
      message,
      { permission }
    )
  }

  static businessRule(message: string, rule?: string): AppError {
    return new AppError(
      ErrorType.BAD_REQUEST,
      ERROR_CODES.BUSINESS_RULE_VIOLATION,
      message,
      { rule }
    )
  }

  static internal(message: string = "内部服务器错误", details?: any): AppError {
    return new AppError(
      ErrorType.INTERNAL,
      ERROR_CODES.INTERNAL_SERVER_ERROR,
      message,
      details
    )
  }

  static database(operation: string, details?: any): AppError {
    return new AppError(
      ErrorType.INTERNAL,
      ERROR_CODES.DATABASE_ERROR,
      `数据库操作失败: ${operation}`,
      details
    )
  }
}

/**
 * 统一错误处理器
 */
export class UnifiedErrorHandler {
  private static requestIdCounter = 0

  /**
   * 生成请求ID
   */
  private static generateRequestId(): string {
    this.requestIdCounter++
    const timestamp = Date.now().toString(36)
    const counter = this.requestIdCounter.toString(36)
    return `req_${timestamp}_${counter}`
  }

  /**
   * 记录错误日志
   */
  private static async logError(
    error: Error | AppError,
    req: NextRequest,
    requestId: string
  ): Promise<void> {
    try {
      const errorData = {
        module: "api",
        level: error instanceof AppError && error.type !== ErrorType.INTERNAL ? "warning" : "error",
        message: error.message,
        details: JSON.stringify({
          requestId,
          url: req.url,
          method: req.method,
          userAgent: req.headers.get("user-agent"),
          timestamp: new Date().toISOString(),
          type: error instanceof AppError ? error.type : "unknown",
          code: error instanceof AppError ? error.code : "UNKNOWN",
          stack: process.env.NODE_ENV === "development" ? error.stack : undefined,
          details: error instanceof AppError ? error.details : undefined
        })
      }

      await prisma.systemLog.create({ data: errorData })
    } catch (logError) {
      console.error("记录错误日志失败:", logError)
    }
  }

  /**
   * 处理并格式化错误响应
   */
  static async handleError(
    error: Error | AppError,
    req: NextRequest
  ): Promise<NextResponse<ApiError>> {
    const requestId = this.generateRequestId()
    
    // 记录错误日志
    await this.logError(error, req, requestId)

    let apiError: ApiError

    if (error instanceof AppError) {
      // 自定义应用错误
      apiError = {
        error: "Request failed",
        type: error.type,
        code: error.code,
        message: error.message,
        details: error.details,
        field: error.field,
        timestamp: new Date().toISOString(),
        requestId,
        statusCode: error.statusCode
      }
    } else {
      // 未知错误
      console.error("未处理的错误:", error)
      
      apiError = {
        error: "Internal server error",
        type: ErrorType.INTERNAL,
        code: ERROR_CODES.INTERNAL_SERVER_ERROR,
        message: process.env.NODE_ENV === "development" 
          ? error.message 
          : "内部服务器错误",
        details: process.env.NODE_ENV === "development" ? {
          stack: error.stack,
          name: error.name
        } : undefined,
        timestamp: new Date().toISOString(),
        requestId,
        statusCode: 500
      }
    }

    return NextResponse.json(apiError, { status: apiError.statusCode })
  }

  /**
   * 成功响应包装器
   */
  static success<T>(data: T, message?: string): NextResponse {
    const response = {
      success: true,
      data,
      message: message || "操作成功",
      timestamp: new Date().toISOString()
    }

    return NextResponse.json(response)
  }

  /**
   * 分页响应包装器
   */
  static paginated<T>(
    data: T[],
    pagination: {
      page: number
      limit: number
      total: number
      totalPages: number
    },
    message?: string
  ): NextResponse {
    const response = {
      success: true,
      data,
      pagination,
      message: message || "获取数据成功",
      timestamp: new Date().toISOString()
    }

    return NextResponse.json(response)
  }
}

/**
 * API路由包装器，提供统一的错误处理
 */
export function withErrorHandler(
  handler: (req: NextRequest, context?: any) => Promise<NextResponse>
) {
  return async (req: NextRequest, context?: any) => {
    try {
      return await handler(req, context)
    } catch (error) {
      return UnifiedErrorHandler.handleError(
        error instanceof Error ? error : new Error(String(error)),
        req
      )
    }
  }
}

/**
 * 数据验证装饰器
 */
export function validateRequest<T>(
  schema: (data: any) => { isValid: boolean; errors: string[]; data?: T }
) {
  return function(
    target: any,
    propertyKey: string,
    descriptor: PropertyDescriptor
  ) {
    const originalMethod = descriptor.value

    descriptor.value = async function(req: NextRequest, ...args: any[]) {
      try {
        const body = await req.json()
        const validation = schema(body)
        
        if (!validation.isValid) {
          throw ErrorFactory.validation(
            `数据验证失败: ${validation.errors.join(", ")}`,
            undefined,
            { errors: validation.errors }
          )
        }

        // 将验证后的数据作为参数传递
        return originalMethod.call(this, req, validation.data, ...args)
      } catch (error) {
        if (error instanceof AppError) {
          throw error
        }
        throw ErrorFactory.validation("请求数据格式无效")
      }
    }

    return descriptor
  }
}

// 导出常用的错误类型和工厂方法
export {
  ErrorType as ApiErrorType,
  ERROR_CODES as ApiErrorCodes,
  ErrorFactory as ApiErrorFactory,
  UnifiedErrorHandler as ApiErrorHandler
}