/**
 * 增强版权限认证中间件
 * 
 * 提供统一、安全、高性能的权限验证机制
 * 解决权限绕过问题，确保API安全
 */

import { NextRequest, NextResponse } from "next/server"
import { getToken } from "next-auth/jwt"
import prisma from "@/lib/db"
import { permissionCache } from "@/lib/permission-cache"

// 权限验证结果接口
interface AuthResult {
  success: boolean
  userId?: string
  email?: string
  error?: string
  statusCode?: number
}

// 权限验证配置接口
interface AuthConfig {
  allowAnonymous?: boolean
  requireSuperAdmin?: boolean
  logFailure?: boolean
  enableCache?: boolean
}

/**
 * 获取用户身份信息
 */
async function getUserIdentity(req: NextRequest): Promise<AuthResult> {
  try {
    const token = await getToken({
      req,
      secret: process.env.NEXTAUTH_SECRET || "linghua-enamel-gallery-secret-key-2024"
    })

    if (!token || !token.id) {
      return {
        success: false,
        error: "未授权访问",
        statusCode: 401
      }
    }

    return {
      success: true,
      userId: token.id as string,
      email: token.email as string
    }
  } catch (error) {
    console.error("获取用户身份失败:", error)
    return {
      success: false,
      error: "身份验证失败",
      statusCode: 500
    }
  }
}

/**
 * 检查超级管理员权限
 */
function isSuperAdmin(email: string): boolean {
  const superAdmins = [
    "<EMAIL>",
    "<EMAIL>"
  ]
  return superAdmins.includes(email.toLowerCase())
}

/**
 * 记录权限验证失败日志
 */
async function logAuthFailure(
  userId: string,
  action: string,
  details: Record<string, any>
): Promise<void> {
  try {
    await prisma.systemLog.create({
      data: {
        module: "auth",
        level: "warning",
        message: `权限验证失败: ${action}`,
        details: JSON.stringify(details),
        userId,
      },
    })
  } catch (error) {
    console.error("记录权限失败日志失败:", error)
  }
}

/**
 * 增强版权限中间件
 * 
 * @param req 请求对象
 * @param permissionCode 权限代码
 * @param config 验证配置
 * @returns 响应对象或undefined（继续处理请求）
 */
export async function withEnhancedPermission(
  req: NextRequest,
  permissionCode: string,
  config: AuthConfig = {}
): Promise<NextResponse | undefined> {
  const {
    allowAnonymous = false,
    requireSuperAdmin = false,
    logFailure = true,
    enableCache = true
  } = config

  try {
    // 获取用户身份
    const identity = await getUserIdentity(req)
    
    if (!identity.success) {
      if (allowAnonymous) {
        return undefined // 允许匿名访问
      }
      return NextResponse.json(
        { error: identity.error },
        { status: identity.statusCode }
      )
    }

    const { userId, email } = identity

    // 超级管理员检查
    if (requireSuperAdmin && !isSuperAdmin(email!)) {
      if (logFailure) {
        await logAuthFailure(userId!, "super_admin_required", {
          permissionCode,
          email,
          url: req.url,
          method: req.method
        })
      }
      return NextResponse.json(
        { error: "需要超级管理员权限" },
        { status: 403 }
      )
    }

    // 超级管理员直接通过
    if (isSuperAdmin(email!)) {
      return undefined
    }

    // 权限检查
    let hasPermission = false
    if (enableCache) {
      hasPermission = await permissionCache.checkUserPermission(userId!, permissionCode)
    } else {
      // 直接查询数据库
      hasPermission = await checkUserPermissionDirect(userId!, permissionCode)
    }

    if (!hasPermission) {
      if (logFailure) {
        await logAuthFailure(userId!, "permission_denied", {
          permissionCode,
          email,
          url: req.url,
          method: req.method
        })
      }
      return NextResponse.json(
        { error: "没有权限执行此操作", permission: permissionCode },
        { status: 403 }
      )
    }

    return undefined // 验证通过，继续处理请求

  } catch (error) {
    console.error("权限验证过程中发生错误:", error)
    return NextResponse.json(
      { error: "权限验证失败", details: error instanceof Error ? error.message : "未知错误" },
      { status: 500 }
    )
  }
}

/**
 * 直接查询数据库检查用户权限
 */
async function checkUserPermissionDirect(userId: string, permissionCode: string): Promise<boolean> {
  try {
    const userRoles = await prisma.userRole.findMany({
      where: { userId },
      include: {
        role: {
          include: {
            rolePermissions: {
              include: {
                permission: true
              }
            }
          }
        }
      }
    })

    // 检查用户是否有指定权限
    for (const userRole of userRoles) {
      for (const rolePermission of userRole.role.rolePermissions) {
        if (rolePermission.permission.code === permissionCode) {
          return true
        }
      }
    }

    return false
  } catch (error) {
    console.error("直接权限检查失败:", error)
    return false
  }
}

/**
 * 增强版角色中间件
 * 
 * @param req 请求对象
 * @param roleCodes 角色代码或代码列表
 * @param config 验证配置
 * @returns 响应对象或undefined（继续处理请求）
 */
export async function withEnhancedRole(
  req: NextRequest,
  roleCodes: string | string[],
  config: AuthConfig = {}
): Promise<NextResponse | undefined> {
  const {
    allowAnonymous = false,
    requireSuperAdmin = false,
    logFailure = true
  } = config

  try {
    // 获取用户身份
    const identity = await getUserIdentity(req)
    
    if (!identity.success) {
      if (allowAnonymous) {
        return undefined
      }
      return NextResponse.json(
        { error: identity.error },
        { status: identity.statusCode }
      )
    }

    const { userId, email } = identity
    const codes = Array.isArray(roleCodes) ? roleCodes : [roleCodes]

    // 超级管理员检查
    if (requireSuperAdmin && !isSuperAdmin(email!)) {
      if (logFailure) {
        await logAuthFailure(userId!, "super_admin_required", {
          requiredRoles: codes,
          email,
          url: req.url,
          method: req.method
        })
      }
      return NextResponse.json(
        { error: "需要超级管理员权限" },
        { status: 403 }
      )
    }

    // 超级管理员直接通过
    if (isSuperAdmin(email!)) {
      return undefined
    }

    // 获取用户角色
    const userRoles = await prisma.userRole.findMany({
      where: { userId },
      include: { role: true },
    })

    // 检查是否有指定角色
    const hasRole = userRoles.some(ur => codes.includes(ur.role.code))

    if (!hasRole) {
      if (logFailure) {
        await logAuthFailure(userId!, "role_denied", {
          requiredRoles: codes,
          userRoles: userRoles.map(ur => ur.role.code),
          email,
          url: req.url,
          method: req.method
        })
      }
      return NextResponse.json(
        { error: "没有权限执行此操作", requiredRoles: codes },
        { status: 403 }
      )
    }

    return undefined

  } catch (error) {
    console.error("角色验证过程中发生错误:", error)
    return NextResponse.json(
      { error: "角色验证失败", details: error instanceof Error ? error.message : "未知错误" },
      { status: 500 }
    )
  }
}

/**
 * 批量权限检查中间件
 * 
 * @param req 请求对象
 * @param permissionCodes 权限代码列表
 * @param requireAll 是否需要所有权限（默认只需要其中一个）
 * @param config 验证配置
 * @returns 响应对象或undefined（继续处理请求）
 */
export async function withBatchPermissions(
  req: NextRequest,
  permissionCodes: string[],
  requireAll: boolean = false,
  config: AuthConfig = {}
): Promise<NextResponse | undefined> {
  try {
    // 获取用户身份
    const identity = await getUserIdentity(req)
    
    if (!identity.success) {
      return NextResponse.json(
        { error: identity.error },
        { status: identity.statusCode }
      )
    }

    const { userId, email } = identity

    // 超级管理员直接通过
    if (isSuperAdmin(email!)) {
      return undefined
    }

    // 批量检查权限
    const permissionResults = await Promise.all(
      permissionCodes.map(code => 
        config.enableCache !== false 
          ? permissionCache.checkUserPermission(userId!, code)
          : checkUserPermissionDirect(userId!, code)
      )
    )

    const hasPermission = requireAll 
      ? permissionResults.every(result => result)
      : permissionResults.some(result => result)

    if (!hasPermission) {
      if (config.logFailure !== false) {
        await logAuthFailure(userId!, "batch_permission_denied", {
          permissionCodes,
          requireAll,
          results: permissionResults,
          email,
          url: req.url,
          method: req.method
        })
      }
      return NextResponse.json(
        { 
          error: requireAll ? "缺少必需的权限" : "没有权限执行此操作",
          requiredPermissions: permissionCodes,
          requireAll
        },
        { status: 403 }
      )
    }

    return undefined

  } catch (error) {
    console.error("批量权限验证失败:", error)
    return NextResponse.json(
      { error: "权限验证失败" },
      { status: 500 }
    )
  }
}

/**
 * 匿名访问中间件（用于公开API）
 */
export async function withAnonymousAccess(): Promise<undefined> {
  return undefined
}

/**
 * 仅超级管理员访问中间件
 */
export async function withSuperAdminOnly(req: NextRequest): Promise<NextResponse | undefined> {
  return withEnhancedPermission(req, "system.admin", {
    requireSuperAdmin: true,
    logFailure: true
  })
}

// 导出便捷方法
export { withPermission, withRole } from "./auth-middleware"

// 权限代码常量
export const PERMISSIONS = {
  // 用户管理
  USERS_VIEW: "users.view",
  USERS_CREATE: "users.create",
  USERS_UPDATE: "users.update",
  USERS_DELETE: "users.delete",
  
  // 角色权限
  ROLES_VIEW: "roles.view",
  ROLES_CREATE: "roles.create",
  ROLES_UPDATE: "roles.update",
  ROLES_DELETE: "roles.delete",
  
  // 权限管理
  PERMISSIONS_VIEW: "permissions.view",
  PERMISSIONS_CREATE: "permissions.create",
  PERMISSIONS_UPDATE: "permissions.update",
  PERMISSIONS_DELETE: "permissions.delete",
  
  // 系统设置
  SETTINGS_VIEW: "settings.view",
  SETTINGS_UPDATE: "settings.update",
  
  // 系统管理
  SYSTEM_ADMIN: "system.admin",
  SYSTEM_BACKUP: "system.backup",
  SYSTEM_LOGS: "system.logs",
  
  // 财务管理
  FINANCE_VIEW: "finance.view",
  FINANCE_CREATE: "finance.create",
  FINANCE_UPDATE: "finance.update",
  FINANCE_DELETE: "finance.delete",
  
  // 员工管理
  EMPLOYEES_VIEW: "employees.view",
  EMPLOYEES_CREATE: "employees.create",
  EMPLOYEES_UPDATE: "employees.update",
  EMPLOYEES_DELETE: "employees.delete"
} as const

// 角色代码常量
export const ROLES = {
  SUPER_ADMIN: "super_admin",
  ADMIN: "admin",
  MANAGER: "manager",
  USER: "user",
  VIEWER: "viewer"
} as const