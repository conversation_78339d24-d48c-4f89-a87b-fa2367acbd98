/**
 * 缓存管理器
 * 
 * 提供多层缓存策略和智能缓存管理
 * 支持内存缓存、Redis缓存和自动失效策略
 */

import { NextRequest } from "next/server"

// 缓存配置接口
export interface CacheConfig {
  ttl: number // 过期时间（秒）
  maxItems?: number // 最大缓存项数
  strategy?: 'lru' | 'lfu' | 'fifo' // 缓存淘汰策略
  keyPrefix?: string // 缓存key前缀
  compress?: boolean // 是否压缩数据
}

// 缓存项接口
export interface CacheItem<T = any> {
  value: T
  expiry: number
  hits: number
  created: number
  lastAccessed: number
}

// 缓存统计接口
export interface CacheStats {
  totalKeys: number
  totalHits: number
  totalMisses: number
  hitRate: number
  memoryUsage: number
  oldestEntry: number
  newestEntry: number
}

/**
 * 内存缓存管理器
 */
export class MemoryCacheManager<T = any> {
  private cache = new Map<string, CacheItem<T>>()
  private config: Required<CacheConfig>
  private stats = {
    hits: 0,
    misses: 0
  }

  constructor(config: CacheConfig) {
    this.config = {
      ttl: config.ttl,
      maxItems: config.maxItems || 1000,
      strategy: config.strategy || 'lru',
      keyPrefix: config.keyPrefix || 'cache',
      compress: config.compress || false
    }

    // 定期清理过期缓存
    setInterval(() => {
      this.cleanup()
    }, 60000) // 每分钟清理一次
  }

  /**
   * 设置缓存
   */
  set(key: string, value: T, customTTL?: number): void {
    const prefixedKey = `${this.config.keyPrefix}:${key}`
    const ttl = customTTL || this.config.ttl
    const now = Date.now()
    
    const item: CacheItem<T> = {
      value,
      expiry: now + (ttl * 1000),
      hits: 0,
      created: now,
      lastAccessed: now
    }

    // 检查缓存容量
    if (this.cache.size >= this.config.maxItems) {
      this.evict()
    }

    this.cache.set(prefixedKey, item)
  }

  /**
   * 获取缓存
   */
  get(key: string): T | null {
    const prefixedKey = `${this.config.keyPrefix}:${key}`
    const item = this.cache.get(prefixedKey)

    if (!item) {
      this.stats.misses++
      return null
    }

    // 检查是否过期
    if (Date.now() > item.expiry) {
      this.cache.delete(prefixedKey)
      this.stats.misses++
      return null
    }

    // 更新访问统计
    item.hits++
    item.lastAccessed = Date.now()
    this.stats.hits++

    return item.value
  }

  /**
   * 删除缓存
   */
  delete(key: string): boolean {
    const prefixedKey = `${this.config.keyPrefix}:${key}`
    return this.cache.delete(prefixedKey)
  }

  /**
   * 清空缓存
   */
  clear(): void {
    this.cache.clear()
    this.stats.hits = 0
    this.stats.misses = 0
  }

  /**
   * 检查缓存是否存在
   */
  has(key: string): boolean {
    const prefixedKey = `${this.config.keyPrefix}:${key}`
    const item = this.cache.get(prefixedKey)
    
    if (!item) return false
    
    // 检查是否过期
    if (Date.now() > item.expiry) {
      this.cache.delete(prefixedKey)
      return false
    }
    
    return true
  }

  /**
   * 获取或设置缓存（如果不存在则执行获取函数）
   */
  async getOrSet<R = T>(
    key: string,
    getter: () => Promise<R> | R,
    customTTL?: number
  ): Promise<R> {
    const cached = this.get(key)
    if (cached !== null) {
      return cached as unknown as R
    }

    const value = await getter()
    this.set(key, value as unknown as T, customTTL)
    return value
  }

  /**
   * 缓存淘汰策略
   */
  private evict(): void {
    if (this.cache.size === 0) return

    let keyToEvict: string | null = null

    switch (this.config.strategy) {
      case 'lru': // 最近最少使用
        keyToEvict = this.findLRU()
        break
      case 'lfu': // 最少频率使用
        keyToEvict = this.findLFU()
        break
      case 'fifo': // 先进先出
        keyToEvict = this.findFIFO()
        break
    }

    if (keyToEvict) {
      this.cache.delete(keyToEvict)
    }
  }

  /**
   * 查找最近最少使用的key
   */
  private findLRU(): string | null {
    let oldestKey: string | null = null
    let oldestTime = Date.now()

    for (const [key, item] of this.cache.entries()) {
      if (item.lastAccessed < oldestTime) {
        oldestTime = item.lastAccessed
        oldestKey = key
      }
    }

    return oldestKey
  }

  /**
   * 查找最少频率使用的key
   */
  private findLFU(): string | null {
    let leastKey: string | null = null
    let leastHits = Infinity

    for (const [key, item] of this.cache.entries()) {
      if (item.hits < leastHits) {
        leastHits = item.hits
        leastKey = key
      }
    }

    return leastKey
  }

  /**
   * 查找最早创建的key（FIFO）
   */
  private findFIFO(): string | null {
    let oldestKey: string | null = null
    let oldestTime = Date.now()

    for (const [key, item] of this.cache.entries()) {
      if (item.created < oldestTime) {
        oldestTime = item.created
        oldestKey = key
      }
    }

    return oldestKey
  }

  /**
   * 清理过期缓存
   */
  private cleanup(): void {
    const now = Date.now()
    const keysToDelete: string[] = []

    for (const [key, item] of this.cache.entries()) {
      if (now > item.expiry) {
        keysToDelete.push(key)
      }
    }

    keysToDelete.forEach(key => {
      this.cache.delete(key)
    })

    if (keysToDelete.length > 0) {
      console.log(`缓存清理: 移除 ${keysToDelete.length} 个过期项`)
    }
  }

  /**
   * 获取缓存统计
   */
  getStats(): CacheStats {
    const items = Array.from(this.cache.values())
    const totalRequests = this.stats.hits + this.stats.misses
    
    return {
      totalKeys: this.cache.size,
      totalHits: this.stats.hits,
      totalMisses: this.stats.misses,
      hitRate: totalRequests > 0 ? (this.stats.hits / totalRequests) * 100 : 0,
      memoryUsage: this.getMemoryUsage(),
      oldestEntry: items.length > 0 ? Math.min(...items.map(i => i.created)) : 0,
      newestEntry: items.length > 0 ? Math.max(...items.map(i => i.created)) : 0
    }
  }

  /**
   * 估算内存使用量（字节）
   */
  private getMemoryUsage(): number {
    let totalSize = 0
    
    for (const [key, item] of this.cache.entries()) {
      totalSize += key.length * 2 // Unicode字符占2字节
      totalSize += JSON.stringify(item.value).length * 2
      totalSize += 64 // 元数据开销估算
    }
    
    return totalSize
  }

  /**
   * 获取所有缓存键
   */
  keys(): string[] {
    return Array.from(this.cache.keys())
  }

  /**
   * 获取缓存项详情
   */
  getItem(key: string): CacheItem<T> | null {
    const prefixedKey = `${this.config.keyPrefix}:${key}`
    return this.cache.get(prefixedKey) || null
  }
}

/**
 * 预定义的缓存实例
 */
export const cacheInstances = {
  // 用户权限缓存 - 短期缓存，高频访问
  permissions: new MemoryCacheManager<string[]>({
    ttl: 300, // 5分钟
    maxItems: 500,
    strategy: 'lru',
    keyPrefix: 'perm'
  }),

  // 用户会话缓存 - 中期缓存
  sessions: new MemoryCacheManager<any>({
    ttl: 1800, // 30分钟
    maxItems: 200,
    strategy: 'lru',
    keyPrefix: 'sess'
  }),

  // 产品数据缓存 - 长期缓存，低频更新
  products: new MemoryCacheManager<any>({
    ttl: 3600, // 1小时
    maxItems: 1000,
    strategy: 'lfu',
    keyPrefix: 'prod'
  }),

  // 财务报表缓存 - 长期缓存
  reports: new MemoryCacheManager<any>({
    ttl: 7200, // 2小时
    maxItems: 100,
    strategy: 'lru',
    keyPrefix: 'rpt'
  }),

  // 系统配置缓存 - 长期缓存
  config: new MemoryCacheManager<any>({
    ttl: 10800, // 3小时
    maxItems: 50,
    strategy: 'fifo',
    keyPrefix: 'cfg'
  }),

  // API响应缓存 - 短期缓存
  api: new MemoryCacheManager<any>({
    ttl: 60, // 1分钟
    maxItems: 2000,
    strategy: 'lru',
    keyPrefix: 'api'
  })
}

/**
 * 缓存装饰器
 */
export function withCache<T>(
  cacheManager: MemoryCacheManager<T>,
  keyGenerator: (req: NextRequest, ...args: any[]) => string,
  ttl?: number
) {
  return function(target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value

    descriptor.value = async function(req: NextRequest, ...args: any[]) {
      const cacheKey = keyGenerator(req, ...args)
      
      // 尝试从缓存获取
      const cached = cacheManager.get(cacheKey)
      if (cached !== null) {
        console.log(`缓存命中: ${cacheKey}`)
        return cached
      }

      // 执行原方法
      const result = await originalMethod.call(this, req, ...args)
      
      // 存储到缓存
      cacheManager.set(cacheKey, result, ttl)
      console.log(`缓存存储: ${cacheKey}`)
      
      return result
    }

    return descriptor
  }
}

/**
 * 生成缓存键
 */
export const cacheKeyGenerators = {
  /**
   * 用户权限缓存键
   */
  userPermissions: (userId: string) => `user_${userId}_permissions`,

  /**
   * 产品列表缓存键
   */
  productList: (req: NextRequest) => {
    const url = new URL(req.url)
    const params = url.searchParams.toString()
    return `products_${params}`
  },

  /**
   * 财务报表缓存键
   */
  financialReport: (type: string, startDate: string, endDate: string) => 
    `financial_${type}_${startDate}_${endDate}`,

  /**
   * 用户会话缓存键
   */
  userSession: (userId: string) => `session_${userId}`,

  /**
   * API响应缓存键
   */
  apiResponse: (req: NextRequest) => {
    const url = new URL(req.url)
    const method = req.method
    const pathname = url.pathname
    const params = url.searchParams.toString()
    return `${method}_${pathname}_${params}`.replace(/[^a-zA-Z0-9_]/g, '_')
  }
}

/**
 * 获取所有缓存统计
 */
export function getAllCacheStats(): Record<string, CacheStats> {
  const stats: Record<string, CacheStats> = {}
  
  for (const [name, cache] of Object.entries(cacheInstances)) {
    stats[name] = cache.getStats()
  }
  
  return stats
}

/**
 * 清空所有缓存
 */
export function clearAllCaches(): void {
  for (const cache of Object.values(cacheInstances)) {
    cache.clear()
  }
  console.log('所有缓存已清空')
}

/**
 * 预热缓存
 */
export async function warmupCache(): Promise<void> {
  console.log('开始缓存预热...')
  
  try {
    // 这里可以添加预热逻辑，比如预加载常用数据
    console.log('缓存预热完成')
  } catch (error) {
    console.error('缓存预热失败:', error)
  }
}