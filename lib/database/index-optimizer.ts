/**
 * 数据库索引优化器
 * 
 * 分析数据库查询模式并提供索引优化建议
 * 监控查询性能并生成优化报告
 */

import prisma from "@/lib/db"

// 索引优化建议接口
export interface IndexSuggestion {
  tableName: string
  columnNames: string[]
  indexType: 'btree' | 'hash' | 'gin' | 'gist'
  reason: string
  estimatedImpact: 'high' | 'medium' | 'low'
  query: string
}

// 查询性能统计接口
export interface QueryPerformanceStats {
  tableName: string
  operation: 'SELECT' | 'INSERT' | 'UPDATE' | 'DELETE'
  avgExecutionTime: number
  totalQueries: number
  slowQueries: number
}

/**
 * 数据库索引优化器类
 */
export class DatabaseIndexOptimizer {
  
  /**
   * 分析高频查询表并生成索引建议
   */
  async generateIndexSuggestions(): Promise<IndexSuggestion[]> {
    const suggestions: IndexSuggestion[] = []

    // 1. 用户认证相关索引
    suggestions.push({
      tableName: 'User',
      columnNames: ['email', 'password'],
      indexType: 'btree',
      reason: '用户登录查询频繁，email+password复合索引提升认证性能',
      estimatedImpact: 'high',
      query: 'CREATE INDEX CONCURRENTLY idx_user_auth ON "User" (email, password) WHERE email IS NOT NULL AND password IS NOT NULL;'
    })

    suggestions.push({
      tableName: 'User',
      columnNames: ['resetToken'],
      indexType: 'btree',
      reason: '密码重置功能需要快速查找重置令牌',
      estimatedImpact: 'medium',
      query: 'CREATE INDEX CONCURRENTLY idx_user_reset_token ON "User" (reset_token) WHERE reset_token IS NOT NULL;'
    })

    // 2. 权限验证相关索引
    suggestions.push({
      tableName: 'UserRole',
      columnNames: ['userId', 'roleId'],
      indexType: 'btree',
      reason: '权限检查时需要快速查找用户角色关系',
      estimatedImpact: 'high',
      query: 'CREATE INDEX CONCURRENTLY idx_user_role_composite ON "UserRole" (user_id, role_id);'
    })

    suggestions.push({
      tableName: 'RolePermission',
      columnNames: ['roleId', 'permissionId'],
      indexType: 'btree',
      reason: '角色权限查询的复合索引',
      estimatedImpact: 'high',
      query: 'CREATE INDEX CONCURRENTLY idx_role_permission_composite ON "RolePermission" (role_id, permission_id);'
    })

    // 3. 财务模块索引
    suggestions.push({
      tableName: 'FinancialTransaction',
      columnNames: ['accountId', 'transactionDate'],
      indexType: 'btree',
      reason: '财务报表查询通常按账户和日期范围过滤',
      estimatedImpact: 'high',
      query: 'CREATE INDEX CONCURRENTLY idx_financial_transaction_account_date ON "FinancialTransaction" (account_id, transaction_date DESC);'
    })

    suggestions.push({
      tableName: 'FinancialTransaction',
      columnNames: ['type', 'createdAt'],
      indexType: 'btree',
      reason: '按交易类型和创建时间查询的性能优化',
      estimatedImpact: 'medium',
      query: 'CREATE INDEX CONCURRENTLY idx_financial_transaction_type_created ON "FinancialTransaction" (type, created_at DESC);'
    })

    // 4. 产品管理索引
    suggestions.push({
      tableName: 'Product',
      columnNames: ['status', 'createdAt'],
      indexType: 'btree',
      reason: '产品列表查询通常按状态过滤并按创建时间排序',
      estimatedImpact: 'medium',
      query: 'CREATE INDEX CONCURRENTLY idx_product_status_created ON "Product" (status, created_at DESC);'
    })

    suggestions.push({
      tableName: 'Product',
      columnNames: ['name'],
      indexType: 'gin',
      reason: '产品名称的全文搜索性能优化',
      estimatedImpact: 'medium',
      query: 'CREATE INDEX CONCURRENTLY idx_product_name_search ON "Product" USING gin (to_tsvector(\'chinese\', name));'
    })

    // 5. 库存管理索引
    suggestions.push({
      tableName: 'InventoryItem',
      columnNames: ['productId', 'warehouseId'],
      indexType: 'btree',
      reason: '库存查询通常按产品和仓库维度进行',
      estimatedImpact: 'high',
      query: 'CREATE INDEX CONCURRENTLY idx_inventory_product_warehouse ON "InventoryItem" (product_id, warehouse_id);'
    })

    suggestions.push({
      tableName: 'InventoryTransaction',
      columnNames: ['productId', 'transactionDate'],
      indexType: 'btree',
      reason: '库存历史记录查询优化',
      estimatedImpact: 'medium',
      query: 'CREATE INDEX CONCURRENTLY idx_inventory_transaction_product_date ON "InventoryTransaction" (product_id, transaction_date DESC);'
    })

    // 6. 订单管理索引
    suggestions.push({
      tableName: 'Order',
      columnNames: ['status', 'orderDate'],
      indexType: 'btree',
      reason: '订单列表查询和状态过滤优化',
      estimatedImpact: 'medium',
      query: 'CREATE INDEX CONCURRENTLY idx_order_status_date ON "Order" (status, order_date DESC);'
    })

    suggestions.push({
      tableName: 'OrderItem',
      columnNames: ['orderId', 'productId'],
      indexType: 'btree',
      reason: '订单详情和产品销量统计查询优化',
      estimatedImpact: 'medium',
      query: 'CREATE INDEX CONCURRENTLY idx_order_item_composite ON "OrderItem" (order_id, product_id);'
    })

    // 7. 员工管理索引
    suggestions.push({
      tableName: 'Employee',
      columnNames: ['status', 'position'],
      indexType: 'btree',
      reason: '员工列表按状态和职位过滤查询优化',
      estimatedImpact: 'low',
      query: 'CREATE INDEX CONCURRENTLY idx_employee_status_position ON "Employee" (status, position);'
    })

    // 8. 系统日志索引
    suggestions.push({
      tableName: 'SystemLog',
      columnNames: ['level', 'createdAt'],
      indexType: 'btree',
      reason: '系统日志查询通常按级别和时间范围过滤',
      estimatedImpact: 'medium',
      query: 'CREATE INDEX CONCURRENTLY idx_system_log_level_created ON "SystemLog" (level, created_at DESC);'
    })

    suggestions.push({
      tableName: 'SystemLog',
      columnNames: ['module', 'createdAt'],
      indexType: 'btree',
      reason: '按模块查询系统日志的性能优化',
      estimatedImpact: 'medium',
      query: 'CREATE INDEX CONCURRENTLY idx_system_log_module_created ON "SystemLog" (module, created_at DESC);'
    })

    // 9. 审计日志索引
    suggestions.push({
      tableName: 'AuditLog',
      columnNames: ['entityType', 'entityId', 'createdAt'],
      indexType: 'btree',
      reason: '审计日志按实体类型和ID查询优化',
      estimatedImpact: 'medium',
      query: 'CREATE INDEX CONCURRENTLY idx_audit_log_entity ON "AuditLog" (entity_type, entity_id, created_at DESC);'
    })

    // 10. 通知系统索引
    suggestions.push({
      tableName: 'Notification',
      columnNames: ['userId', 'read', 'createdAt'],
      indexType: 'btree',
      reason: '用户通知查询按读取状态和时间排序',
      estimatedImpact: 'medium',
      query: 'CREATE INDEX CONCURRENTLY idx_notification_user_read_created ON "Notification" (user_id, read, created_at DESC);'
    })

    return suggestions
  }

  /**
   * 执行索引创建
   */
  async executeIndexCreation(suggestions: IndexSuggestion[]): Promise<{
    created: string[]
    failed: Array<{ query: string; error: string }>
  }> {
    const created: string[] = []
    const failed: Array<{ query: string; error: string }> = []

    for (const suggestion of suggestions) {
      try {
        console.log(`创建索引: ${suggestion.tableName} - ${suggestion.columnNames.join(', ')}`)
        
        // 执行索引创建SQL
        await prisma.$executeRawUnsafe(suggestion.query)
        
        created.push(`${suggestion.tableName}.${suggestion.columnNames.join('_')}`)
        
        // 记录索引创建日志
        await prisma.systemLog.create({
          data: {
            module: 'database',
            level: 'info',
            message: `索引创建成功: ${suggestion.tableName}`,
            details: JSON.stringify({
              tableName: suggestion.tableName,
              columns: suggestion.columnNames,
              indexType: suggestion.indexType,
              reason: suggestion.reason,
              impact: suggestion.estimatedImpact
            })
          }
        })
        
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '未知错误'
        failed.push({
          query: suggestion.query,
          error: errorMessage
        })
        
        console.error(`索引创建失败: ${suggestion.tableName}`, error)
        
        // 记录错误日志
        await prisma.systemLog.create({
          data: {
            module: 'database',
            level: 'error',
            message: `索引创建失败: ${suggestion.tableName}`,
            details: JSON.stringify({
              tableName: suggestion.tableName,
              error: errorMessage,
              query: suggestion.query
            })
          }
        })
      }
    }

    return { created, failed }
  }

  /**
   * 分析现有索引使用情况
   */
  async analyzeIndexUsage(): Promise<Array<{
    schemaname: string
    tablename: string
    indexname: string
    idx_scan: number
    idx_tup_read: number
    idx_tup_fetch: number
  }>> {
    try {
      // 查询PostgreSQL的索引使用统计
      const indexStats = await prisma.$queryRaw`
        SELECT 
          schemaname,
          tablename,
          indexname,
          idx_scan,
          idx_tup_read,
          idx_tup_fetch
        FROM pg_stat_user_indexes 
        WHERE schemaname = 'public'
        ORDER BY idx_scan DESC;
      `
      
      return indexStats as Array<{
        schemaname: string
        tablename: string
        indexname: string
        idx_scan: number
        idx_tup_read: number
        idx_tup_fetch: number
      }>
    } catch (error) {
      console.error('分析索引使用情况失败:', error)
      return []
    }
  }

  /**
   * 检测未使用的索引
   */
  async detectUnusedIndexes(): Promise<Array<{
    schemaname: string
    tablename: string
    indexname: string
    size: string
  }>> {
    try {
      // 查询未使用的索引
      const unusedIndexes = await prisma.$queryRaw`
        SELECT 
          schemaname,
          tablename,
          indexname,
          pg_size_pretty(pg_relation_size(indexrelid)) as size
        FROM pg_stat_user_indexes 
        WHERE idx_scan = 0 
          AND schemaname = 'public'
          AND indexname NOT LIKE '%_pkey'
        ORDER BY pg_relation_size(indexrelid) DESC;
      `
      
      return unusedIndexes as Array<{
        schemaname: string
        tablename: string
        indexname: string
        size: string
      }>
    } catch (error) {
      console.error('检测未使用索引失败:', error)
      return []
    }
  }

  /**
   * 生成数据库优化报告
   */
  async generateOptimizationReport(): Promise<{
    suggestions: IndexSuggestion[]
    currentIndexUsage: any[]
    unusedIndexes: any[]
    totalTables: number
    totalIndexes: number
    optimization: {
      highImpactSuggestions: number
      mediumImpactSuggestions: number
      lowImpactSuggestions: number
    }
  }> {
    try {
      const suggestions = await this.generateIndexSuggestions()
      const currentIndexUsage = await this.analyzeIndexUsage()
      const unusedIndexes = await this.detectUnusedIndexes()

      // 计算表和索引数量
      const tablesInfo = await prisma.$queryRaw`
        SELECT COUNT(*) as table_count 
        FROM information_schema.tables 
        WHERE table_schema = 'public';
      `
      
      const indexesInfo = await prisma.$queryRaw`
        SELECT COUNT(*) as index_count 
        FROM pg_stat_user_indexes 
        WHERE schemaname = 'public';
      `

      const totalTables = (tablesInfo as any)[0]?.table_count || 0
      const totalIndexes = (indexesInfo as any)[0]?.index_count || 0

      // 统计建议影响级别
      const optimization = {
        highImpactSuggestions: suggestions.filter(s => s.estimatedImpact === 'high').length,
        mediumImpactSuggestions: suggestions.filter(s => s.estimatedImpact === 'medium').length,
        lowImpactSuggestions: suggestions.filter(s => s.estimatedImpact === 'low').length
      }

      return {
        suggestions,
        currentIndexUsage,
        unusedIndexes,
        totalTables,
        totalIndexes,
        optimization
      }
    } catch (error) {
      console.error('生成优化报告失败:', error)
      throw error
    }
  }
}

/**
 * 创建数据库优化器单例
 */
export const databaseOptimizer = new DatabaseIndexOptimizer()