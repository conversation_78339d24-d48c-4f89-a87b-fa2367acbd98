/**
 * 财务模块增强功能
 * 
 * 提供高级财务分析、数据验证和性能优化功能
 */

export interface FinancialValidationRules {
  account: {
    nameMinLength: number;
    nameMaxLength: number;
    maxInitialBalance: number;
    allowedAccountTypes: string[];
  };
  transaction: {
    minAmount: number;
    maxAmount: number;
    requiredFields: string[];
    allowedTypes: string[];
    allowedPaymentMethods: string[];
  };
  category: {
    codePattern: RegExp;
    nameMinLength: number;
    nameMaxLength: number;
    maxDepth: number;
  };
}

export const defaultValidationRules: FinancialValidationRules = {
  account: {
    nameMinLength: 2,
    nameMaxLength: 50,
    maxInitialBalance: ********0, // 1亿
    allowedAccountTypes: ['bank', 'cash', 'alipay', 'wechat', 'other']
  },
  transaction: {
    minAmount: 0.01,
    maxAmount: ********, // 1000万
    requiredFields: ['amount', 'type', 'accountId', 'transactionDate'],
    allowedTypes: ['income', 'expense', 'transfer'],
    allowedPaymentMethods: ['cash', 'bank_transfer', 'alipay', 'wechat', 'credit_card', 'other']
  },
  category: {
    codePattern: /^[A-Z][A-Z0-9_]{2,19}$/,
    nameMinLength: 2,
    nameMaxLength: 30,
    maxDepth: 3
  }
};

/**
 * 财务账户验证器
 */
export class FinancialAccountValidator {
  constructor(private rules: FinancialValidationRules['account']) {}

  validate(account: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // 名称验证
    if (!account.name || typeof account.name !== 'string') {
      errors.push('账户名称为必填项且必须为字符串');
    } else {
      if (account.name.length < this.rules.nameMinLength) {
        errors.push(`账户名称长度不能少于${this.rules.nameMinLength}个字符`);
      }
      if (account.name.length > this.rules.nameMaxLength) {
        errors.push(`账户名称长度不能超过${this.rules.nameMaxLength}个字符`);
      }
    }

    // 账户类型验证
    if (!account.accountType || !this.rules.allowedAccountTypes.includes(account.accountType)) {
      errors.push(`账户类型必须为: ${this.rules.allowedAccountTypes.join(', ')}`);
    }

    // 初始余额验证
    if (typeof account.initialBalance !== 'number' || isNaN(account.initialBalance)) {
      errors.push('初始余额必须为有效数字');
    } else {
      if (account.initialBalance < 0) {
        errors.push('初始余额不能为负数');
      }
      if (account.initialBalance > this.rules.maxInitialBalance) {
        errors.push(`初始余额不能超过${this.rules.maxInitialBalance}`);
      }
    }

    // 账户号码验证（银行账户必须有）
    if (account.accountType === 'bank' && !account.accountNumber) {
      errors.push('银行账户必须提供账户号码');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

/**
 * 财务交易验证器
 */
export class FinancialTransactionValidator {
  constructor(private rules: FinancialValidationRules['transaction']) {}

  validate(transaction: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // 必填字段验证
    for (const field of this.rules.requiredFields) {
      if (!transaction[field]) {
        errors.push(`${field} 为必填项`);
      }
    }

    // 金额验证
    if (typeof transaction.amount !== 'number' || isNaN(transaction.amount)) {
      errors.push('交易金额必须为有效数字');
    } else {
      if (transaction.amount < this.rules.minAmount) {
        errors.push(`交易金额不能少于${this.rules.minAmount}`);
      }
      if (transaction.amount > this.rules.maxAmount) {
        errors.push(`交易金额不能超过${this.rules.maxAmount}`);
      }
    }

    // 交易类型验证
    if (!this.rules.allowedTypes.includes(transaction.type)) {
      errors.push(`交易类型必须为: ${this.rules.allowedTypes.join(', ')}`);
    }

    // 支付方式验证
    if (transaction.paymentMethod && !this.rules.allowedPaymentMethods.includes(transaction.paymentMethod)) {
      errors.push(`支付方式必须为: ${this.rules.allowedPaymentMethods.join(', ')}`);
    }

    // 日期验证
    if (transaction.transactionDate) {
      const date = new Date(transaction.transactionDate);
      if (isNaN(date.getTime())) {
        errors.push('交易日期格式无效');
      } else if (date > new Date()) {
        errors.push('交易日期不能为未来时间');
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

/**
 * 财务分类验证器
 */
export class FinancialCategoryValidator {
  constructor(private rules: FinancialValidationRules['category']) {}

  validate(category: any, existingCategories: any[] = []): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // 名称验证
    if (!category.name || typeof category.name !== 'string') {
      errors.push('分类名称为必填项且必须为字符串');
    } else {
      if (category.name.length < this.rules.nameMinLength) {
        errors.push(`分类名称长度不能少于${this.rules.nameMinLength}个字符`);
      }
      if (category.name.length > this.rules.nameMaxLength) {
        errors.push(`分类名称长度不能超过${this.rules.nameMaxLength}个字符`);
      }
    }

    // 代码验证
    if (!category.code || !this.rules.codePattern.test(category.code)) {
      errors.push('分类代码格式无效，必须以大写字母开头，包含大写字母、数字和下划线，长度3-20位');
    }

    // 代码唯一性验证
    if (existingCategories.some(c => c.code === category.code && c.id !== category.id)) {
      errors.push('分类代码已存在');
    }

    // 类型验证
    if (!category.type || !['income', 'expense'].includes(category.type)) {
      errors.push('分类类型必须为 income 或 expense');
    }

    // 层级深度验证
    if (category.parentId) {
      const depth = this.calculateCategoryDepth(category.parentId, existingCategories);
      if (depth >= this.rules.maxDepth) {
        errors.push(`分类层级不能超过${this.rules.maxDepth}级`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  private calculateCategoryDepth(parentId: number, categories: any[]): number {
    const parent = categories.find(c => c.id === parentId);
    if (!parent || !parent.parentId) {
      return 1;
    }
    return 1 + this.calculateCategoryDepth(parent.parentId, categories);
  }
}

/**
 * 财务数据分析器
 */
export class FinancialAnalyzer {
  /**
   * 计算月度财务趋势
   */
  static calculateMonthlyTrend(transactions: any[], months: number = 12): any[] {
    const now = new Date();
    const trends = [];

    for (let i = months - 1; i >= 0; i--) {
      const targetDate = new Date(now.getFullYear(), now.getMonth() - i, 1);
      const monthStart = new Date(targetDate.getFullYear(), targetDate.getMonth(), 1);
      const monthEnd = new Date(targetDate.getFullYear(), targetDate.getMonth() + 1, 0);

      const monthTransactions = transactions.filter(t => {
        const transDate = new Date(t.transactionDate);
        return transDate >= monthStart && transDate <= monthEnd && t.status === 'completed';
      });

      const income = monthTransactions
        .filter(t => t.type === 'income')
        .reduce((sum, t) => sum + t.amount, 0);

      const expense = monthTransactions
        .filter(t => t.type === 'expense')
        .reduce((sum, t) => sum + t.amount, 0);

      trends.push({
        month: targetDate.toISOString().substring(0, 7), // YYYY-MM
        monthName: targetDate.toLocaleDateString('zh-CN', { year: 'numeric', month: 'long' }),
        income,
        expense,
        profit: income - expense,
        profitMargin: income > 0 ? ((income - expense) / income) * 100 : 0
      });
    }

    return trends;
  }

  /**
   * 计算分类占比分析
   */
  static calculateCategoryBreakdown(transactions: any[], categories: any[]): any {
    const incomeTotal = transactions
      .filter(t => t.type === 'income' && t.status === 'completed')
      .reduce((sum, t) => sum + t.amount, 0);

    const expenseTotal = transactions
      .filter(t => t.type === 'expense' && t.status === 'completed')
      .reduce((sum, t) => sum + t.amount, 0);

    const incomeBreakdown = categories
      .filter(c => c.type === 'income')
      .map(category => {
        const amount = transactions
          .filter(t => t.categoryId === category.id && t.type === 'income' && t.status === 'completed')
          .reduce((sum, t) => sum + t.amount, 0);

        return {
          category: category.name,
          amount,
          percentage: incomeTotal > 0 ? (amount / incomeTotal) * 100 : 0
        };
      })
      .filter(item => item.amount > 0)
      .sort((a, b) => b.amount - a.amount);

    const expenseBreakdown = categories
      .filter(c => c.type === 'expense')
      .map(category => {
        const amount = transactions
          .filter(t => t.categoryId === category.id && t.type === 'expense' && t.status === 'completed')
          .reduce((sum, t) => sum + t.amount, 0);

        return {
          category: category.name,
          amount,
          percentage: expenseTotal > 0 ? (amount / expenseTotal) * 100 : 0
        };
      })
      .filter(item => item.amount > 0)
      .sort((a, b) => b.amount - a.amount);

    return {
      income: incomeBreakdown,
      expense: expenseBreakdown,
      totals: {
        income: incomeTotal,
        expense: expenseTotal
      }
    };
  }

  /**
   * 计算现金流分析
   */
  static calculateCashFlow(transactions: any[], accounts: any[]): any {
    const cashFlowByAccount = accounts.map(account => {
      const accountTransactions = transactions.filter(t => t.accountId === account.id);
      
      const inflow = accountTransactions
        .filter(t => t.type === 'income' && t.status === 'completed')
        .reduce((sum, t) => sum + t.amount, 0);

      const outflow = accountTransactions
        .filter(t => t.type === 'expense' && t.status === 'completed')
        .reduce((sum, t) => sum + t.amount, 0);

      return {
        accountName: account.name,
        accountType: account.accountType,
        currentBalance: account.currentBalance,
        inflow,
        outflow,
        netFlow: inflow - outflow,
        transactionCount: accountTransactions.length
      };
    });

    const totalInflow = cashFlowByAccount.reduce((sum, account) => sum + account.inflow, 0);
    const totalOutflow = cashFlowByAccount.reduce((sum, account) => sum + account.outflow, 0);
    const totalBalance = cashFlowByAccount.reduce((sum, account) => sum + account.currentBalance, 0);

    return {
      accounts: cashFlowByAccount,
      summary: {
        totalBalance,
        totalInflow,
        totalOutflow,
        netCashFlow: totalInflow - totalOutflow,
        burnRate: totalOutflow > 0 ? totalBalance / (totalOutflow / 30) : Infinity // 以天为单位的资金消耗率
      }
    };
  }

  /**
   * 财务健康度评分
   */
  static calculateFinancialHealth(summary: any): { score: number; grade: string; recommendations: string[] } {
    let score = 0;
    const recommendations: string[] = [];

    // 盈利能力 (30分)
    if (summary.netProfit > 0) {
      if (summary.profitMargin >= 20) score += 30;
      else if (summary.profitMargin >= 10) score += 20;
      else if (summary.profitMargin >= 5) score += 10;
      else score += 5;
    } else {
      recommendations.push('当前处于亏损状态，需要增加收入或减少支出');
    }

    // 现金流稳定性 (25分)
    if (summary.totalBalance > summary.totalExpense * 3) {
      score += 25; // 有3个月以上的运营资金
    } else if (summary.totalBalance > summary.totalExpense) {
      score += 15; // 有1个月以上的运营资金
    } else if (summary.totalBalance > 0) {
      score += 5;
      recommendations.push('现金储备不足，建议增加流动资金');
    } else {
      recommendations.push('现金流为负，存在财务风险');
    }

    // 收入多样性 (20分)
    // 这里需要传入更详细的收入分析数据，暂时给予中等分数
    score += 10;
    recommendations.push('建议多样化收入来源，降低经营风险');

    // 成本控制 (25分)
    if (summary.totalExpense > 0 && summary.totalIncome > 0) {
      const expenseRatio = summary.totalExpense / summary.totalIncome;
      if (expenseRatio <= 0.7) score += 25;
      else if (expenseRatio <= 0.8) score += 20;
      else if (expenseRatio <= 0.9) score += 15;
      else if (expenseRatio <= 1.0) score += 10;
      else recommendations.push('支出过高，需要加强成本控制');
    }

    let grade: string;
    if (score >= 90) grade = 'A+';
    else if (score >= 80) grade = 'A';
    else if (score >= 70) grade = 'B';
    else if (score >= 60) grade = 'C';
    else grade = 'D';

    return { score, grade, recommendations };
  }
}

/**
 * 财务性能优化器
 */
export class FinancialPerformanceOptimizer {
  /**
   * 批量处理财务数据
   */
  static batchProcessTransactions(transactions: any[], batchSize: number = 100): any[][] {
    const batches: any[][] = [];
    for (let i = 0; i < transactions.length; i += batchSize) {
      batches.push(transactions.slice(i, i + batchSize));
    }
    return batches;
  }

  /**
   * 缓存财务统计结果
   */
  static createFinancialCache() {
    const cache = new Map<string, { data: any; timestamp: number; ttl: number }>();

    return {
      get: (key: string) => {
        const item = cache.get(key);
        if (item && Date.now() - item.timestamp < item.ttl) {
          return item.data;
        }
        cache.delete(key);
        return null;
      },
      set: (key: string, data: any, ttl: number = 300000) => { // 默认5分钟缓存
        cache.set(key, { data, timestamp: Date.now(), ttl });
      },
      clear: () => {
        cache.clear();
      },
      size: () => cache.size
    };
  }

  /**
   * 财务数据索引优化
   */
  static buildFinancialIndexes(transactions: any[]) {
    const indexes = {
      byAccount: new Map<number, any[]>(),
      byCategory: new Map<number, any[]>(),
      byDate: new Map<string, any[]>(),
      byType: new Map<string, any[]>()
    };

    transactions.forEach(transaction => {
      // 按账户索引
      if (!indexes.byAccount.has(transaction.accountId)) {
        indexes.byAccount.set(transaction.accountId, []);
      }
      indexes.byAccount.get(transaction.accountId)!.push(transaction);

      // 按分类索引
      if (transaction.categoryId) {
        if (!indexes.byCategory.has(transaction.categoryId)) {
          indexes.byCategory.set(transaction.categoryId, []);
        }
        indexes.byCategory.get(transaction.categoryId)!.push(transaction);
      }

      // 按日期索引（按月）
      const monthKey = new Date(transaction.transactionDate).toISOString().substring(0, 7);
      if (!indexes.byDate.has(monthKey)) {
        indexes.byDate.set(monthKey, []);
      }
      indexes.byDate.get(monthKey)!.push(transaction);

      // 按类型索引
      if (!indexes.byType.has(transaction.type)) {
        indexes.byType.set(transaction.type, []);
      }
      indexes.byType.get(transaction.type)!.push(transaction);
    });

    return indexes;
  }
}

/**
 * 导出所有增强功能
 */
export const FinancialEnhancements = {
  validators: {
    FinancialAccountValidator,
    FinancialTransactionValidator,
    FinancialCategoryValidator
  },
  analyzer: FinancialAnalyzer,
  optimizer: FinancialPerformanceOptimizer,
  rules: defaultValidationRules
};