/**
 * 财务报表生成增强功能
 * 
 * 提供多格式财务报表生成、高级分析和数据可视化功能
 */

import ExcelJS from 'exceljs';
import jsPDF from 'jspdf';
import 'jspdf-autotable';
import { format, startOfMonth, endOfMonth, subMonths, addDays } from 'date-fns';
import { zhCN } from 'date-fns/locale';

// 扩展 jsPDF 类型
declare module 'jspdf' {
  interface jsPDF {
    autoTable: (options: any) => jsPDF;
  }
}

export interface ReportConfig {
  title: string;
  period: {
    start: Date;
    end: Date;
  };
  includeCharts: boolean;
  includeAnalysis: boolean;
  includeComparisons: boolean;
  format: 'excel' | 'pdf' | 'csv' | 'json';
  templateType: 'standard' | 'executive' | 'detailed' | 'custom';
}

export interface FinancialReportData {
  accounts: Array<{
    id: number;
    name: string;
    type: string;
    balance: number;
    transactions: number;
  }>;
  transactions: Array<{
    id: number;
    date: Date;
    amount: number;
    type: 'income' | 'expense' | 'transfer';
    category: string;
    account: string;
    description: string;
    counterparty?: string;
  }>;
  categories: Array<{
    id: number;
    name: string;
    type: 'income' | 'expense';
    total: number;
    count: number;
  }>;
  summary: {
    totalIncome: number;
    totalExpense: number;
    netProfit: number;
    profitMargin: number;
    cashFlow: number;
    averageTransaction: number;
  };
}

/**
 * 财务报表生成器
 */
export class FinancialReportGenerator {
  private config: ReportConfig;
  private data: FinancialReportData;

  constructor(config: ReportConfig, data: FinancialReportData) {
    this.config = config;
    this.data = data;
  }

  /**
   * 生成Excel报表
   */
  async generateExcelReport(): Promise<Buffer> {
    const workbook = new ExcelJS.Workbook();
    workbook.creator = '聆花掐丝珐琅馆财务系统';
    workbook.created = new Date();
    workbook.modified = new Date();

    // 创建封面页
    await this.createCoverSheet(workbook);

    // 创建财务摘要页
    await this.createSummarySheet(workbook);

    // 创建交易明细页
    await this.createTransactionSheet(workbook);

    // 创建账户分析页
    await this.createAccountAnalysisSheet(workbook);

    // 创建分类分析页
    await this.createCategoryAnalysisSheet(workbook);

    // 创建趋势分析页
    if (this.config.includeAnalysis) {
      await this.createTrendAnalysisSheet(workbook);
    }

    // 创建对比分析页
    if (this.config.includeComparisons) {
      await this.createComparisonSheet(workbook);
    }

    return await workbook.xlsx.writeBuffer() as Buffer;
  }

  /**
   * 生成PDF报表
   */
  async generatePDFReport(): Promise<Buffer> {
    const doc = new jsPDF();
    
    // 设置中文字体
    doc.addFont('/fonts/NotoSansCJK-Regular.ttf', 'NotoSansCJK', 'normal');
    doc.setFont('NotoSansCJK');

    // 添加封面页
    this.addPDFCoverPage(doc);

    // 添加财务摘要
    doc.addPage();
    this.addPDFSummaryPage(doc);

    // 添加图表（如果启用）
    if (this.config.includeCharts) {
      doc.addPage();
      await this.addPDFChartsPage(doc);
    }

    // 添加详细数据
    doc.addPage();
    this.addPDFDetailPage(doc);

    return Buffer.from(doc.output('arraybuffer'));
  }

  /**
   * 生成CSV报表
   */
  generateCSVReport(): string {
    const lines: string[] = [];
    
    // 添加标题行
    lines.push(`财务报表,${this.config.title}`);
    lines.push(`报表期间,${format(this.config.period.start, 'yyyy-MM-dd')} 至 ${format(this.config.period.end, 'yyyy-MM-dd')}`);
    lines.push(`生成时间,${format(new Date(), 'yyyy-MM-dd HH:mm:ss')}`);
    lines.push('');

    // 添加摘要数据
    lines.push('财务摘要');
    lines.push('项目,金额');
    lines.push(`总收入,${this.data.summary.totalIncome}`);
    lines.push(`总支出,${this.data.summary.totalExpense}`);
    lines.push(`净利润,${this.data.summary.netProfit}`);
    lines.push(`利润率,${this.data.summary.profitMargin}%`);
    lines.push('');

    // 添加账户数据
    lines.push('账户余额');
    lines.push('账户名称,账户类型,余额,交易数量');
    this.data.accounts.forEach(account => {
      lines.push(`${account.name},${account.type},${account.balance},${account.transactions}`);
    });
    lines.push('');

    // 添加分类数据
    lines.push('分类统计');
    lines.push('分类名称,类型,总金额,交易笔数');
    this.data.categories.forEach(category => {
      lines.push(`${category.name},${category.type},${category.total},${category.count}`);
    });
    lines.push('');

    // 添加交易明细
    lines.push('交易明细');
    lines.push('日期,金额,类型,分类,账户,描述,对方');
    this.data.transactions.forEach(transaction => {
      lines.push([
        format(transaction.date, 'yyyy-MM-dd'),
        transaction.amount.toString(),
        transaction.type,
        transaction.category,
        transaction.account,
        transaction.description,
        transaction.counterparty || ''
      ].join(','));
    });

    return lines.join('\n');
  }

  /**
   * 创建封面页
   */
  private async createCoverSheet(workbook: ExcelJS.Workbook): Promise<void> {
    const sheet = workbook.addWorksheet('封面');
    
    // 设置页面布局
    sheet.pageSetup.paperSize = 9; // A4
    sheet.pageSetup.orientation = 'portrait';

    // 添加标题
    sheet.mergeCells('A1:H5');
    const titleCell = sheet.getCell('A1');
    titleCell.value = this.config.title;
    titleCell.font = { size: 24, bold: true, color: { argb: 'FF1F497D' } };
    titleCell.alignment = { horizontal: 'center', vertical: 'middle' };

    // 添加副标题
    sheet.mergeCells('A6:H7');
    const subtitleCell = sheet.getCell('A6');
    subtitleCell.value = `报表期间: ${format(this.config.period.start, 'yyyy年MM月dd日', { locale: zhCN })} 至 ${format(this.config.period.end, 'yyyy年MM月dd日', { locale: zhCN })}`;
    subtitleCell.font = { size: 14, color: { argb: 'FF666666' } };
    subtitleCell.alignment = { horizontal: 'center', vertical: 'middle' };

    // 添加生成信息
    sheet.mergeCells('A10:H11');
    const infoCell = sheet.getCell('A10');
    infoCell.value = `生成时间: ${format(new Date(), 'yyyy年MM月dd日 HH:mm:ss', { locale: zhCN })}`;
    infoCell.font = { size: 12, color: { argb: 'FF999999' } };
    infoCell.alignment = { horizontal: 'center', vertical: 'middle' };

    // 添加公司信息
    const companyInfo = [
      '聆花掐丝珐琅馆',
      '地址: [公司地址]',
      '电话: [联系电话]',
      '邮箱: [联系邮箱]'
    ];

    companyInfo.forEach((info, index) => {
      const cell = sheet.getCell(`A${15 + index}`);
      cell.value = info;
      cell.font = { size: 11, color: { argb: 'FF666666' } };
      cell.alignment = { horizontal: 'center' };
    });
  }

  /**
   * 创建财务摘要页
   */
  private async createSummarySheet(workbook: ExcelJS.Workbook): Promise<void> {
    const sheet = workbook.addWorksheet('财务摘要');

    // 设置列宽
    sheet.columns = [
      { width: 20 },
      { width: 15 },
      { width: 15 },
      { width: 20 },
      { width: 15 }
    ];

    // 添加标题
    sheet.mergeCells('A1:E1');
    const titleCell = sheet.getCell('A1');
    titleCell.value = '财务摘要';
    titleCell.font = { size: 16, bold: true };
    titleCell.alignment = { horizontal: 'center' };

    // 添加关键指标
    const metrics = [
      ['指标', '金额 (元)', '占比', '说明', '状态'],
      ['总收入', this.data.summary.totalIncome, '100%', '报表期间内所有收入', this.getStatusIcon(this.data.summary.totalIncome > 0)],
      ['总支出', this.data.summary.totalExpense, `${((this.data.summary.totalExpense / this.data.summary.totalIncome) * 100).toFixed(1)}%`, '报表期间内所有支出', this.getStatusIcon(this.data.summary.totalExpense < this.data.summary.totalIncome)],
      ['净利润', this.data.summary.netProfit, `${this.data.summary.profitMargin.toFixed(1)}%`, '收入减去支出', this.getStatusIcon(this.data.summary.netProfit > 0)],
      ['现金流', this.data.summary.cashFlow, '-', '净现金流量', this.getStatusIcon(this.data.summary.cashFlow > 0)],
      ['平均交易额', this.data.summary.averageTransaction, '-', '单笔交易平均金额', '-']
    ];

    metrics.forEach((row, index) => {
      row.forEach((value, colIndex) => {
        const cell = sheet.getCell(index + 3, colIndex + 1);
        cell.value = value;
        if (index === 0) {
          cell.font = { bold: true };
          cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF2F2F2' } };
        }
        if (colIndex === 1 && index > 0) {
          cell.numFmt = '¥#,##0.00';
        }
      });
    });

    // 添加边框
    const range = sheet.getCell('A3').address + ':' + sheet.getCell(`E${metrics.length + 2}`).address;
    sheet.getCell(range).border = {
      top: { style: 'thin' },
      left: { style: 'thin' },
      bottom: { style: 'thin' },
      right: { style: 'thin' }
    };
  }

  /**
   * 创建交易明细页
   */
  private async createTransactionSheet(workbook: ExcelJS.Workbook): Promise<void> {
    const sheet = workbook.addWorksheet('交易明细');

    // 设置列
    sheet.columns = [
      { header: '日期', key: 'date', width: 12 },
      { header: '金额', key: 'amount', width: 12 },
      { header: '类型', key: 'type', width: 8 },
      { header: '分类', key: 'category', width: 15 },
      { header: '账户', key: 'account', width: 15 },
      { header: '描述', key: 'description', width: 25 },
      { header: '对方', key: 'counterparty', width: 15 }
    ];

    // 设置标题样式
    sheet.getRow(1).font = { bold: true };
    sheet.getRow(1).fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF2F2F2' } };

    // 添加数据
    this.data.transactions.forEach(transaction => {
      sheet.addRow({
        date: format(transaction.date, 'yyyy-MM-dd'),
        amount: transaction.amount,
        type: transaction.type === 'income' ? '收入' : transaction.type === 'expense' ? '支出' : '转账',
        category: transaction.category,
        account: transaction.account,
        description: transaction.description,
        counterparty: transaction.counterparty || '-'
      });
    });

    // 设置金额列格式
    sheet.getColumn('amount').numFmt = '¥#,##0.00';

    // 添加自动筛选
    sheet.autoFilter = 'A1:G1';

    // 冻结首行
    sheet.views = [{ state: 'frozen', ySplit: 1 }];
  }

  /**
   * 创建账户分析页
   */
  private async createAccountAnalysisSheet(workbook: ExcelJS.Workbook): Promise<void> {
    const sheet = workbook.addWorksheet('账户分析');

    // 设置列
    sheet.columns = [
      { header: '账户名称', key: 'name', width: 20 },
      { header: '账户类型', key: 'type', width: 12 },
      { header: '当前余额', key: 'balance', width: 15 },
      { header: '交易笔数', key: 'transactions', width: 12 },
      { header: '余额占比', key: 'percentage', width: 12 },
      { header: '活跃度', key: 'activity', width: 10 }
    ];

    // 设置标题样式
    sheet.getRow(1).font = { bold: true };
    sheet.getRow(1).fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF2F2F2' } };

    // 计算总余额
    const totalBalance = this.data.accounts.reduce((sum, account) => sum + account.balance, 0);

    // 添加数据
    this.data.accounts.forEach(account => {
      const percentage = totalBalance > 0 ? (account.balance / totalBalance * 100).toFixed(1) + '%' : '0%';
      const activity = account.transactions > 10 ? '高' : account.transactions > 5 ? '中' : '低';

      sheet.addRow({
        name: account.name,
        type: this.getAccountTypeLabel(account.type),
        balance: account.balance,
        transactions: account.transactions,
        percentage: percentage,
        activity: activity
      });
    });

    // 设置格式
    sheet.getColumn('balance').numFmt = '¥#,##0.00';

    // 添加总计行
    const totalRow = sheet.addRow({
      name: '总计',
      type: '-',
      balance: totalBalance,
      transactions: this.data.accounts.reduce((sum, account) => sum + account.transactions, 0),
      percentage: '100%',
      activity: '-'
    });
    totalRow.font = { bold: true };
    totalRow.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFFF2CC' } };
  }

  /**
   * 创建分类分析页
   */
  private async createCategoryAnalysisSheet(workbook: ExcelJS.Workbook): Promise<void> {
    const sheet = workbook.addWorksheet('分类分析');

    // 设置列
    sheet.columns = [
      { header: '分类名称', key: 'name', width: 20 },
      { header: '类型', key: 'type', width: 8 },
      { header: '总金额', key: 'total', width: 15 },
      { header: '交易笔数', key: 'count', width: 12 },
      { header: '平均金额', key: 'average', width: 15 },
      { header: '占比', key: 'percentage', width: 12 }
    ];

    // 设置标题样式
    sheet.getRow(1).font = { bold: true };
    sheet.getRow(1).fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF2F2F2' } };

    // 分别计算收入和支出总额
    const totalIncome = this.data.categories.filter(c => c.type === 'income').reduce((sum, c) => sum + c.total, 0);
    const totalExpense = this.data.categories.filter(c => c.type === 'expense').reduce((sum, c) => sum + c.total, 0);

    // 添加数据
    this.data.categories.forEach(category => {
      const totalForType = category.type === 'income' ? totalIncome : totalExpense;
      const percentage = totalForType > 0 ? (category.total / totalForType * 100).toFixed(1) + '%' : '0%';
      const average = category.count > 0 ? category.total / category.count : 0;

      sheet.addRow({
        name: category.name,
        type: category.type === 'income' ? '收入' : '支出',
        total: category.total,
        count: category.count,
        average: average,
        percentage: percentage
      });
    });

    // 设置格式
    sheet.getColumn('total').numFmt = '¥#,##0.00';
    sheet.getColumn('average').numFmt = '¥#,##0.00';
  }

  /**
   * 创建趋势分析页
   */
  private async createTrendAnalysisSheet(workbook: ExcelJS.Workbook): Promise<void> {
    const sheet = workbook.addWorksheet('趋势分析');

    // 这里可以添加月度趋势、增长率分析等
    // 根据交易数据计算各月份的收入支出趋势

    sheet.addRow(['月份', '收入', '支出', '净利润', '同比增长']);
    
    // 模拟月度数据（实际应用中需要根据真实数据计算）
    const monthlyData = this.calculateMonthlyTrends();
    monthlyData.forEach(month => {
      sheet.addRow([month.period, month.income, month.expense, month.profit, month.growth + '%']);
    });

    // 设置格式
    sheet.getRow(1).font = { bold: true };
    sheet.getColumn('B').numFmt = '¥#,##0.00';
    sheet.getColumn('C').numFmt = '¥#,##0.00';
    sheet.getColumn('D').numFmt = '¥#,##0.00';
  }

  /**
   * 创建对比分析页
   */
  private async createComparisonSheet(workbook: ExcelJS.Workbook): Promise<void> {
    const sheet = workbook.addWorksheet('对比分析');

    // 添加同期对比数据
    sheet.addRow(['指标', '本期', '上期', '变化', '变化率']);
    
    // 这里需要获取上期数据进行对比
    const comparisonData = this.calculateComparisons();
    comparisonData.forEach(item => {
      sheet.addRow([item.metric, item.current, item.previous, item.change, item.changeRate + '%']);
    });

    // 设置格式
    sheet.getRow(1).font = { bold: true };
  }

  /**
   * PDF封面页
   */
  private addPDFCoverPage(doc: jsPDF): void {
    // 添加标题
    doc.setFontSize(24);
    doc.text(this.config.title, 105, 60, { align: 'center' });

    // 添加副标题
    doc.setFontSize(14);
    const periodText = `报表期间: ${format(this.config.period.start, 'yyyy年MM月dd日')} 至 ${format(this.config.period.end, 'yyyy年MM月dd日')}`;
    doc.text(periodText, 105, 80, { align: 'center' });

    // 添加生成时间
    doc.setFontSize(12);
    const generatedText = `生成时间: ${format(new Date(), 'yyyy年MM月dd日 HH:mm:ss')}`;
    doc.text(generatedText, 105, 100, { align: 'center' });
  }

  /**
   * PDF摘要页
   */
  private addPDFSummaryPage(doc: jsPDF): void {
    doc.setFontSize(16);
    doc.text('财务摘要', 20, 20);

    const summaryData = [
      ['指标', '金额 (元)'],
      ['总收入', this.formatCurrency(this.data.summary.totalIncome)],
      ['总支出', this.formatCurrency(this.data.summary.totalExpense)],
      ['净利润', this.formatCurrency(this.data.summary.netProfit)],
      ['利润率', this.data.summary.profitMargin.toFixed(1) + '%']
    ];

    doc.autoTable({
      head: [summaryData[0]],
      body: summaryData.slice(1),
      startY: 30,
      theme: 'grid',
      styles: { fontSize: 12 }
    });
  }

  /**
   * PDF图表页
   */
  private async addPDFChartsPage(doc: jsPDF): Promise<void> {
    doc.setFontSize(16);
    doc.text('数据图表', 20, 20);

    // 这里可以添加图表生成逻辑
    // 需要使用图表库（如Chart.js）生成图片后插入PDF
    doc.setFontSize(12);
    doc.text('图表功能正在开发中...', 20, 40);
  }

  /**
   * PDF详细数据页
   */
  private addPDFDetailPage(doc: jsPDF): void {
    doc.setFontSize(16);
    doc.text('交易明细', 20, 20);

    const transactionData = this.data.transactions.slice(0, 20).map(t => [
      format(t.date, 'MM-dd'),
      this.formatCurrency(t.amount),
      t.type === 'income' ? '收入' : '支出',
      t.category,
      t.description.substring(0, 20) + (t.description.length > 20 ? '...' : '')
    ]);

    doc.autoTable({
      head: [['日期', '金额', '类型', '分类', '描述']],
      body: transactionData,
      startY: 30,
      theme: 'grid',
      styles: { fontSize: 10 }
    });
  }

  /**
   * 辅助方法
   */
  private getStatusIcon(condition: boolean): string {
    return condition ? '✓' : '✗';
  }

  private getAccountTypeLabel(type: string): string {
    const labels: { [key: string]: string } = {
      'bank': '银行账户',
      'cash': '现金',
      'alipay': '支付宝',
      'wechat': '微信支付',
      'other': '其他'
    };
    return labels[type] || type;
  }

  private formatCurrency(amount: number): string {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY'
    }).format(amount);
  }

  private calculateMonthlyTrends(): Array<{
    period: string;
    income: number;
    expense: number;
    profit: number;
    growth: number;
  }> {
    // 模拟数据，实际应用中需要根据真实交易数据计算
    return [
      { period: '2025-03', income: 85000, expense: 45000, profit: 40000, growth: 12.5 },
      { period: '2025-04', income: 92000, expense: 48000, profit: 44000, growth: 10.0 },
      { period: '2025-05', income: 88000, expense: 52000, profit: 36000, growth: -18.2 }
    ];
  }

  private calculateComparisons(): Array<{
    metric: string;
    current: number;
    previous: number;
    change: number;
    changeRate: number;
  }> {
    // 模拟对比数据
    return [
      { metric: '总收入', current: 265000, previous: 240000, change: 25000, changeRate: 10.4 },
      { metric: '总支出', current: 145000, previous: 138000, change: 7000, changeRate: 5.1 },
      { metric: '净利润', current: 120000, previous: 102000, change: 18000, changeRate: 17.6 }
    ];
  }
}

/**
 * 报表模板管理器
 */
export class ReportTemplateManager {
  /**
   * 获取预定义模板
   */
  static getTemplate(type: string): Partial<ReportConfig> {
    const templates: { [key: string]: Partial<ReportConfig> } = {
      'monthly': {
        title: '月度财务报表',
        includeCharts: true,
        includeAnalysis: true,
        includeComparisons: true,
        templateType: 'standard'
      },
      'quarterly': {
        title: '季度财务报表',
        includeCharts: true,
        includeAnalysis: true,
        includeComparisons: true,
        templateType: 'executive'
      },
      'annual': {
        title: '年度财务报表',
        includeCharts: true,
        includeAnalysis: true,
        includeComparisons: true,
        templateType: 'detailed'
      },
      'simple': {
        title: '简易财务报表',
        includeCharts: false,
        includeAnalysis: false,
        includeComparisons: false,
        templateType: 'standard'
      }
    };

    return templates[type] || templates['simple'];
  }

  /**
   * 自定义模板验证
   */
  static validateTemplate(config: ReportConfig): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!config.title) {
      errors.push('报表标题不能为空');
    }

    if (!config.period.start || !config.period.end) {
      errors.push('报表期间不能为空');
    }

    if (config.period.start > config.period.end) {
      errors.push('开始日期不能晚于结束日期');
    }

    if (!['excel', 'pdf', 'csv', 'json'].includes(config.format)) {
      errors.push('不支持的报表格式');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }
}

/**
 * 导出工具函数
 */
export const FinancialReporting = {
  FinancialReportGenerator,
  ReportTemplateManager
};