/**
 * 权限缓存模块
 * 提供权限数据的缓存和管理功能
 */

import prisma from "@/lib/db"
import { cacheInstances, cacheKeyGenerators } from "./cache/cache-manager"

// 权限缓存接口
export interface PermissionCacheInterface {
  getUserPermissions(userId: string): Promise<string[]>
  checkUserPermission(userId: string, permissionCode: string): Promise<boolean>
  clearUserPermissions(userId: string): void
  clearAllPermissions(): void
}

/**
 * 增强的权限缓存实现
 */
class EnhancedPermissionCache implements PermissionCacheInterface {
  
  /**
   * 获取用户权限列表（带缓存）
   */
  async getUserPermissions(userId: string): Promise<string[]> {
    const cacheKey = cacheKeyGenerators.userPermissions(userId)
    
    return await cacheInstances.permissions.getOrSet(
      cacheKey,
      async () => {
        console.log(`从数据库获取用户权限: ${userId}`)
        
        // 从数据库获取用户权限
        const userRoles = await prisma.userRole.findMany({
          where: { userId },
          include: {
            role: {
              include: {
                rolePermissions: {
                  include: {
                    permission: true
                  }
                }
              }
            }
          }
        })

        // 提取所有权限代码
        const permissions = new Set<string>()
        
        for (const userRole of userRoles) {
          for (const rolePermission of userRole.role.rolePermissions) {
            permissions.add(rolePermission.permission.code)
          }
        }

        return Array.from(permissions)
      },
      300 // 5分钟缓存
    )
  }

  /**
   * 检查用户是否有指定权限（带缓存）
   */
  async checkUserPermission(userId: string, permissionCode: string): Promise<boolean> {
    try {
      // 首先检查用户是否存在
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { id: true, email: true }
      })

      if (!user) {
        console.warn(`用户不存在: ${userId}`)
        return false
      }

      // 超级管理员检查
      if (user.email === "<EMAIL>") {
        console.log(`超级管理员权限通过: ${user.email}`)
        return true
      }

      // 获取用户权限列表
      const userPermissions = await this.getUserPermissions(userId)
      
      // 检查是否有指定权限
      const hasPermission = userPermissions.includes(permissionCode)
      
      console.log(`权限检查: 用户 ${userId} ${hasPermission ? '有' : '无'} ${permissionCode} 权限`)
      
      return hasPermission
    } catch (error) {
      console.error(`权限检查失败: ${userId} -> ${permissionCode}`, error)
      return false
    }
  }

  /**
   * 清除指定用户的权限缓存
   */
  clearUserPermissions(userId: string): void {
    const cacheKey = cacheKeyGenerators.userPermissions(userId)
    cacheInstances.permissions.delete(cacheKey)
    console.log(`已清除用户权限缓存: ${userId}`)
  }

  /**
   * 清除所有权限缓存
   */
  clearAllPermissions(): void {
    cacheInstances.permissions.clear()
    console.log('已清除所有权限缓存')
  }

  /**
   * 预热用户权限缓存
   */
  async warmupUserPermissions(userIds: string[]): Promise<void> {
    console.log(`开始预热 ${userIds.length} 个用户的权限缓存`)
    
    const promises = userIds.map(userId => this.getUserPermissions(userId))
    await Promise.allSettled(promises)
    
    console.log('用户权限缓存预热完成')
  }

  /**
   * 获取权限缓存统计
   */
  getStats() {
    return cacheInstances.permissions.getStats()
  }
}

// 导出增强的权限缓存实例
export const permissionCache = new EnhancedPermissionCache()

// 兼容性：保持原有接口
export interface PermissionCacheCompat {
  get(key: string): Promise<any>
  set(key: string, value: any, ttl?: number): Promise<void>
  delete(key: string): Promise<void>
  clear(): Promise<void>
  getUserPermissions(userId: string): Promise<string[]>
  checkUserPermission(userId: string, permissionCode: string): Promise<boolean>
}

// 提供兼容的接口实现
const compatCache: PermissionCacheCompat = {
  async get(key: string) {
    return cacheInstances.permissions.get(key)
  },
  
  async set(key: string, value: any, ttl: number = 300) {
    cacheInstances.permissions.set(key, value, ttl)
  },
  
  async delete(key: string) {
    cacheInstances.permissions.delete(key)
  },
  
  async clear() {
    cacheInstances.permissions.clear()
  },
  
  async getUserPermissions(userId: string) {
    return permissionCache.getUserPermissions(userId)
  },
  
  async checkUserPermission(userId: string, permissionCode: string) {
    return permissionCache.checkUserPermission(userId, permissionCode)
  }
}

// 导出兼容接口
export { compatCache as permissionCacheCompat }

// 权限检查函数
export async function checkPermission(userId: string, permission: string): Promise<boolean> {
  const cacheKey = `permission:${userId}:${permission}`
  
  try {
    // 尝试从缓存获取
    const cached = await permissionCache.get(cacheKey)
    if (cached !== null) {
      return cached
    }

    // 这里应该调用实际的权限检查逻辑
    // 暂时返回 true 作为默认值
    const hasPermission = true
    
    // 缓存结果
    await permissionCache.set(cacheKey, hasPermission, 300000) // 5分钟缓存
    
    return hasPermission
  } catch (error) {
    console.error('权限检查失败:', error)
    return false
  }
}

// 清除用户权限缓存
export async function clearUserPermissionCache(userId: string): Promise<void> {
  // 这里应该实现清除特定用户的所有权限缓存
  // 暂时清除所有缓存
  await permissionCache.clear()
}
