/**
 * 统一设置管理系统
 * 
 * 提供统一的设置读写、缓存管理和变更通知功能
 */

import prisma from '@/lib/db'
import { configManager } from '@/lib/config-manager'

// 设置类型定义
export interface SettingValue {
  id?: number
  key: string
  value: string
  description?: string
  group: string
  type: 'string' | 'number' | 'boolean' | 'json' | 'select'
  options?: string
  isSystem: boolean
  isReadonly: boolean
  validationRules?: string
  defaultValue?: string
  createdAt?: Date
  updatedAt?: Date
}

export interface SettingGroup {
  group: string
  title: string
  description: string
  icon: string
  order: number
  settings: SettingValue[]
}

export interface SystemInfo {
  name: string
  version: string
  environment: string
  database: string
  nodeVersion: string
  memoryUsage: {
    used: number
    total: number
    percentage: number
  }
  uptime: number
}

/**
 * 设置验证器
 */
export class SettingValidator {
  static validate(setting: SettingValue, value: string): { valid: boolean; error?: string } {
    try {
      switch (setting.type) {
        case 'boolean':
          if (!['true', 'false'].includes(value.toLowerCase())) {
            return { valid: false, error: '值必须为 true 或 false' }
          }
          break

        case 'number':
          const num = Number(value)
          if (isNaN(num)) {
            return { valid: false, error: '值必须为有效数字' }
          }
          // 检查验证规则
          if (setting.validationRules) {
            const rules = JSON.parse(setting.validationRules)
            if (rules.min !== undefined && num < rules.min) {
              return { valid: false, error: `值不能小于 ${rules.min}` }
            }
            if (rules.max !== undefined && num > rules.max) {
              return { valid: false, error: `值不能大于 ${rules.max}` }
            }
          }
          break

        case 'select':
          if (setting.options) {
            const options = setting.options.split(',').map(opt => opt.trim())
            if (!options.includes(value)) {
              return { valid: false, error: `值必须为: ${options.join(', ')}` }
            }
          }
          break

        case 'json':
          try {
            JSON.parse(value)
          } catch {
            return { valid: false, error: '值必须为有效的JSON格式' }
          }
          break

        case 'string':
        default:
          // 检查字符串长度
          if (setting.validationRules) {
            const rules = JSON.parse(setting.validationRules)
            if (rules.minLength && value.length < rules.minLength) {
              return { valid: false, error: `长度不能少于 ${rules.minLength} 个字符` }
            }
            if (rules.maxLength && value.length > rules.maxLength) {
              return { valid: false, error: `长度不能超过 ${rules.maxLength} 个字符` }
            }
            if (rules.pattern && !new RegExp(rules.pattern).test(value)) {
              return { valid: false, error: '值格式不正确' }
            }
          }
          break
      }

      return { valid: true }
    } catch (error) {
      return { valid: false, error: '验证过程中发生错误' }
    }
  }
}

/**
 * 统一设置管理器
 */
export class SettingsManager {
  private static instance: SettingsManager
  private cache = new Map<string, any>()
  private cacheExpiry = new Map<string, number>()
  private readonly CACHE_TTL = 5 * 60 * 1000 // 5分钟缓存

  static getInstance(): SettingsManager {
    if (!SettingsManager.instance) {
      SettingsManager.instance = new SettingsManager()
    }
    return SettingsManager.instance
  }

  /**
   * 获取所有设置分组
   */
  async getSettingGroups(): Promise<SettingGroup[]> {
    const cacheKey = 'setting-groups'
    const cached = this.getFromCache(cacheKey)
    if (cached) return cached

    try {
      // 获取所有设置
      const settings = await prisma.systemParameter.findMany({
        orderBy: [
          { group: 'asc' },
          { key: 'asc' }
        ]
      })

      // 如果没有设置，初始化默认设置
      if (settings.length === 0) {
        await this.initializeDefaultSettings()
        return this.getSettingGroups() // 递归调用获取初始化后的设置
      }

      // 按组分类设置
      const groupMap = new Map<string, SettingValue[]>()
      settings.forEach(setting => {
        if (!groupMap.has(setting.group)) {
          groupMap.set(setting.group, [])
        }
        groupMap.get(setting.group)!.push({
          id: setting.id,
          key: setting.key,
          value: setting.value,
          description: setting.description || '',
          group: setting.group,
          type: setting.type as any,
          options: setting.options || '',
          isSystem: setting.isSystem,
          isReadonly: setting.isReadonly,
          validationRules: setting.validationRules || '',
          defaultValue: setting.defaultValue || '',
          createdAt: setting.createdAt,
          updatedAt: setting.updatedAt
        })
      })

      // 构建设置组
      const groups: SettingGroup[] = [
        {
          group: 'general',
          title: '基本设置',
          description: '系统基本配置和公司信息',
          icon: 'Settings',
          order: 1,
          settings: groupMap.get('general') || []
        },
        {
          group: 'company',
          title: '公司信息',
          description: '公司基本信息和联系方式',
          icon: 'Building',
          order: 2,
          settings: groupMap.get('company') || []
        },
        {
          group: 'security',
          title: '安全设置',
          description: '安全策略和权限配置',
          icon: 'Shield',
          order: 3,
          settings: groupMap.get('security') || []
        },
        {
          group: 'notification',
          title: '通知设置',
          description: '邮件和消息通知配置',
          icon: 'Bell',
          order: 4,
          settings: groupMap.get('notification') || []
        },
        {
          group: 'system',
          title: '系统配置',
          description: '系统运行和维护参数',
          icon: 'Database',
          order: 5,
          settings: groupMap.get('system') || []
        },
        {
          group: 'format',
          title: '格式设置',
          description: '日期、时间和数字格式',
          icon: 'Calendar',
          order: 6,
          settings: groupMap.get('format') || []
        }
      ].filter(group => group.settings.length > 0) // 只返回有设置的组

      this.setCache(cacheKey, groups)
      return groups
    } catch (error) {
      console.error('获取设置分组失败:', error)
      throw new Error('获取设置分组失败')
    }
  }

  /**
   * 获取单个设置值
   */
  async getSetting(key: string): Promise<string | null> {
    try {
      const setting = await prisma.systemParameter.findUnique({
        where: { key }
      })
      return setting?.value || null
    } catch (error) {
      console.error(`获取设置 ${key} 失败:`, error)
      return null
    }
  }

  /**
   * 获取多个设置值
   */
  async getSettings(keys: string[]): Promise<Record<string, string>> {
    try {
      const settings = await prisma.systemParameter.findMany({
        where: {
          key: { in: keys }
        }
      })

      const result: Record<string, string> = {}
      settings.forEach(setting => {
        result[setting.key] = setting.value
      })

      return result
    } catch (error) {
      console.error('批量获取设置失败:', error)
      throw new Error('批量获取设置失败')
    }
  }

  /**
   * 更新单个设置
   */
  async updateSetting(key: string, value: string): Promise<void> {
    try {
      // 获取设置定义进行验证
      const setting = await prisma.systemParameter.findUnique({
        where: { key }
      })

      if (!setting) {
        throw new Error(`设置 ${key} 不存在`)
      }

      if (setting.isReadonly) {
        throw new Error(`设置 ${key} 为只读，不能修改`)
      }

      // 验证值
      const validation = SettingValidator.validate(setting as any, value)
      if (!validation.valid) {
        throw new Error(validation.error)
      }

      // 更新设置
      await prisma.systemParameter.update({
        where: { key },
        data: { 
          value,
          updatedAt: new Date()
        }
      })

      // 清除相关缓存
      this.clearCache('setting-groups')
      
      // 使用配置管理器同步缓存
      await configManager.set(key, value, setting.type)

    } catch (error) {
      console.error(`更新设置 ${key} 失败:`, error)
      throw error
    }
  }

  /**
   * 批量更新设置
   */
  async updateSettings(updates: Record<string, string>): Promise<void> {
    try {
      // 获取所有相关设置进行验证
      const keys = Object.keys(updates)
      const settings = await prisma.systemParameter.findMany({
        where: {
          key: { in: keys }
        }
      })

      // 验证所有设置
      for (const setting of settings) {
        const value = updates[setting.key]
        if (value !== undefined) {
          if (setting.isReadonly) {
            throw new Error(`设置 ${setting.key} 为只读，不能修改`)
          }

          const validation = SettingValidator.validate(setting as any, value)
          if (!validation.valid) {
            throw new Error(`${setting.key}: ${validation.error}`)
          }
        }
      }

      // 批量更新设置
      const updatePromises = settings.map(setting => {
        const value = updates[setting.key]
        if (value !== undefined) {
          return prisma.systemParameter.update({
            where: { key: setting.key },
            data: { 
              value,
              updatedAt: new Date()
            }
          })
        }
        return Promise.resolve()
      })

      await Promise.all(updatePromises)

      // 清除缓存
      this.clearCache('setting-groups')

      // 使用配置管理器批量同步
      await configManager.setMultiple(updates)

    } catch (error) {
      console.error('批量更新设置失败:', error)
      throw error
    }
  }

  /**
   * 创建新设置
   */
  async createSetting(setting: Omit<SettingValue, 'id' | 'createdAt' | 'updatedAt'>): Promise<SettingValue> {
    try {
      // 检查设置是否已存在
      const existing = await prisma.systemParameter.findUnique({
        where: { key: setting.key }
      })

      if (existing) {
        throw new Error(`设置 ${setting.key} 已存在`)
      }

      // 验证值
      const validation = SettingValidator.validate(setting, setting.value)
      if (!validation.valid) {
        throw new Error(validation.error)
      }

      // 创建设置
      const newSetting = await prisma.systemParameter.create({
        data: {
          key: setting.key,
          value: setting.value,
          description: setting.description || '',
          group: setting.group,
          type: setting.type,
          options: setting.options || '',
          isSystem: setting.isSystem,
          isReadonly: setting.isReadonly,
          validationRules: setting.validationRules || '',
          defaultValue: setting.defaultValue || ''
        }
      })

      // 清除缓存
      this.clearCache('setting-groups')

      return {
        id: newSetting.id,
        key: newSetting.key,
        value: newSetting.value,
        description: newSetting.description || '',
        group: newSetting.group,
        type: newSetting.type as any,
        options: newSetting.options || '',
        isSystem: newSetting.isSystem,
        isReadonly: newSetting.isReadonly,
        validationRules: newSetting.validationRules || '',
        defaultValue: newSetting.defaultValue || '',
        createdAt: newSetting.createdAt,
        updatedAt: newSetting.updatedAt
      }
    } catch (error) {
      console.error('创建设置失败:', error)
      throw error
    }
  }

  /**
   * 删除设置
   */
  async deleteSetting(key: string): Promise<void> {
    try {
      const setting = await prisma.systemParameter.findUnique({
        where: { key }
      })

      if (!setting) {
        throw new Error(`设置 ${key} 不存在`)
      }

      if (setting.isSystem) {
        throw new Error(`系统设置 ${key} 不能删除`)
      }

      await prisma.systemParameter.delete({
        where: { key }
      })

      // 清除缓存
      this.clearCache('setting-groups')

    } catch (error) {
      console.error(`删除设置 ${key} 失败:`, error)
      throw error
    }
  }

  /**
   * 获取系统信息
   */
  async getSystemInfo(): Promise<SystemInfo> {
    try {
      const memUsage = process.memoryUsage()
      const totalMem = memUsage.heapTotal
      const usedMem = memUsage.heapUsed

      return {
        name: await this.getSetting('system.name') || '聆花掐丝珐琅馆ERP系统',
        version: await this.getSetting('system.version') || '1.0.0',
        environment: process.env.NODE_ENV || 'development',
        database: 'PostgreSQL',
        nodeVersion: process.version,
        memoryUsage: {
          used: Math.round(usedMem / 1024 / 1024), // MB
          total: Math.round(totalMem / 1024 / 1024), // MB
          percentage: Math.round((usedMem / totalMem) * 100)
        },
        uptime: Math.round(process.uptime())
      }
    } catch (error) {
      console.error('获取系统信息失败:', error)
      throw new Error('获取系统信息失败')
    }
  }

  /**
   * 重置设置到默认值
   */
  async resetToDefaults(group?: string): Promise<void> {
    try {
      const whereClause = group ? { group, isSystem: true } : { isSystem: true }
      
      const settings = await prisma.systemParameter.findMany({
        where: whereClause
      })

      const resetPromises = settings.map(setting => {
        if (setting.defaultValue) {
          return this.updateSetting(setting.key, setting.defaultValue)
        }
        return Promise.resolve()
      })

      await Promise.all(resetPromises)

    } catch (error) {
      console.error('重置设置失败:', error)
      throw new Error('重置设置失败')
    }
  }

  /**
   * 初始化默认设置
   */
  private async initializeDefaultSettings(): Promise<void> {
    const defaultSettings = [
      // 基本设置
      { key: "system.name", value: "聆花掐丝珐琅馆ERP系统", description: "系统名称", group: "general", type: "string", isSystem: true, isReadonly: false, defaultValue: "聆花掐丝珐琅馆ERP系统" },
      { key: "system.version", value: "1.0.0", description: "系统版本", group: "general", type: "string", isSystem: true, isReadonly: true, defaultValue: "1.0.0" },
      { key: "system.maintenance", value: "false", description: "维护模式", group: "general", type: "boolean", isSystem: true, isReadonly: false, defaultValue: "false" },
      
      // 公司信息
      { key: "company.name", value: "聆花掐丝珐琅馆", description: "公司名称", group: "company", type: "string", isSystem: false, isReadonly: false, defaultValue: "聆花掐丝珐琅馆" },
      { key: "company.address", value: "北京市朝阳区", description: "公司地址", group: "company", type: "string", isSystem: false, isReadonly: false, defaultValue: "" },
      { key: "company.phone", value: "010-12345678", description: "公司电话", group: "company", type: "string", isSystem: false, isReadonly: false, defaultValue: "" },
      { key: "company.email", value: "<EMAIL>", description: "公司邮箱", group: "company", type: "string", isSystem: false, isReadonly: false, defaultValue: "" },
      
      // 安全设置
      { key: "security.session_timeout", value: "3600", description: "会话超时时间（秒）", group: "security", type: "number", isSystem: true, isReadonly: false, defaultValue: "3600", validationRules: '{"min":300,"max":86400}' },
      { key: "security.password_min_length", value: "8", description: "密码最小长度", group: "security", type: "number", isSystem: true, isReadonly: false, defaultValue: "8", validationRules: '{"min":6,"max":50}' },
      { key: "security.max_login_attempts", value: "5", description: "最大登录尝试次数", group: "security", type: "number", isSystem: true, isReadonly: false, defaultValue: "5", validationRules: '{"min":3,"max":20}' },
      
      // 通知设置
      { key: "notification.enabled", value: "true", description: "启用通知", group: "notification", type: "boolean", isSystem: false, isReadonly: false, defaultValue: "true" },
      { key: "notification.email_enabled", value: "false", description: "启用邮件通知", group: "notification", type: "boolean", isSystem: false, isReadonly: false, defaultValue: "false" },
      { key: "notification.sms_enabled", value: "false", description: "启用短信通知", group: "notification", type: "boolean", isSystem: false, isReadonly: false, defaultValue: "false" },
      
      // 系统配置
      { key: "system.backup_auto", value: "true", description: "自动备份", group: "system", type: "boolean", isSystem: true, isReadonly: false, defaultValue: "true" },
      { key: "system.backup_interval", value: "24", description: "备份间隔（小时）", group: "system", type: "number", isSystem: true, isReadonly: false, defaultValue: "24", validationRules: '{"min":1,"max":168}' },
      { key: "system.log_level", value: "info", description: "日志级别", group: "system", type: "select", options: "debug,info,warn,error", isSystem: true, isReadonly: false, defaultValue: "info" },
      
      // 格式设置
      { key: "format.date", value: "YYYY-MM-DD", description: "日期格式", group: "format", type: "select", options: "YYYY-MM-DD,DD/MM/YYYY,MM/DD/YYYY", isSystem: false, isReadonly: false, defaultValue: "YYYY-MM-DD" },
      { key: "format.time", value: "HH:mm:ss", description: "时间格式", group: "format", type: "select", options: "HH:mm:ss,HH:mm,hh:mm:ss A,hh:mm A", isSystem: false, isReadonly: false, defaultValue: "HH:mm:ss" },
      { key: "format.currency", value: "CNY", description: "货币单位", group: "format", type: "select", options: "CNY,USD,EUR,JPY", isSystem: false, isReadonly: false, defaultValue: "CNY" }
    ]

    await prisma.systemParameter.createMany({
      data: defaultSettings.map(setting => ({
        key: setting.key,
        value: setting.value,
        description: setting.description,
        group: setting.group,
        type: setting.type,
        options: setting.options || null,
        isSystem: setting.isSystem,
        isReadonly: setting.isReadonly,
        validationRules: setting.validationRules || null,
        defaultValue: setting.defaultValue
      })),
      skipDuplicates: true
    })
  }

  /**
   * 缓存管理
   */
  private getFromCache(key: string): any {
    const expiry = this.cacheExpiry.get(key)
    if (expiry && Date.now() > expiry) {
      this.cache.delete(key)
      this.cacheExpiry.delete(key)
      return null
    }
    return this.cache.get(key)
  }

  private setCache(key: string, value: any): void {
    this.cache.set(key, value)
    this.cacheExpiry.set(key, Date.now() + this.CACHE_TTL)
  }

  private clearCache(key?: string): void {
    if (key) {
      this.cache.delete(key)
      this.cacheExpiry.delete(key)
    } else {
      this.cache.clear()
      this.cacheExpiry.clear()
    }
  }
}

// 导出单例实例
export const settingsManager = SettingsManager.getInstance()

// 导出类型和实用函数
export { SettingsManager, SettingValidator }