{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:turbo": "next dev --turbo", "build": "next build", "build:analyze": "ANALYZE=true next build", "build:profile": "next build --profile", "start": "next start", "start:prod": "NODE_ENV=production next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "check:model-sync": "ts-node scripts/check-model-sync.ts", "fix:model-sync": "ts-node scripts/fix-model-sync.ts", "prepare": "husky install", "docs": "typedoc", "docs:watch": "typedoc --watch", "sentry:sourcemaps": "sentry-cli sourcemaps inject ./next && sentry-cli sourcemaps upload ./next", "db:generate": "prisma generate", "db:push": "prisma db push", "db:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts", "clean": "rm -rf .next out dist", "clean:all": "rm -rf .next out dist node_modules && npm install", "performance:audit": "lighthouse http://localhost:3000 --output=html --output-path=./reports/lighthouse.html", "performance:bundle": "npx @next/bundle-analyzer", "security:audit": "npm audit", "security:fix": "npm audit fix", "precommit": "lint-staged", "postbuild": "next-sitemap"}, "dependencies": {"@auth/core": "0.34.2", "@auth/prisma-adapter": "^2.9.1", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "latest", "@prisma/client": "latest", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "latest", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "latest", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@sentry/nextjs": "^8.0.0", "@tanstack/react-table": "^8.21.3", "@tanstack/react-virtual": "^3.13.8", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.20", "bcryptjs": "latest", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.4", "critters": "^0.0.23", "date-fns": "^3.0.0", "echarts": "^5.6.0", "embla-carousel-react": "8.5.1", "exceljs": "latest", "framer-motion": "^12.11.4", "fs": "latest", "input-otp": "1.4.1", "js-cookie": "^3.0.5", "jspdf": "^3.0.1", "lucide-react": "^0.454.0", "next": "^14.0.4", "next-auth": "^4.24.11", "next-themes": "^0.4.4", "nodemailer": "latest", "path": "latest", "prisma": "latest", "react": "^18.3.1", "react-day-picker": "8.10.1", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-hook-form": "latest", "react-resizable-panels": "^2.1.7", "recharts": "^2.15.3", "sonner": "^1.7.1", "swr": "^2.3.3", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vaul": "^0.9.6", "web-vitals": "^5.0.1", "xlsx": "^0.18.5", "zod": "latest"}, "devDependencies": {"@next/bundle-analyzer": "^15.3.4", "@playwright/test": "^1.52.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/js-cookie": "^3.0.6", "@types/node": "^22", "@types/react": "^18", "@types/react-dom": "^18", "@vitejs/plugin-react": "^4.6.0", "husky": "^9.0.11", "jsdom": "^26.1.0", "postcss": "^8", "tailwindcss": "^3.4.17", "ts-node": "^10.9.2", "typedoc": "^0.25.12", "typescript": "^5", "vitest": "^3.1.3"}}