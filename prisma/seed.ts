import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('开始种子数据...')

  // 创建默认角色
  const adminRole = await prisma.role.upsert({
    where: { code: 'admin' },
    update: {},
    create: {
      name: '管理员',
      code: 'admin',
      description: '系统管理员',
      isSystem: true,
    },
  })

  const userRole = await prisma.role.upsert({
    where: { code: 'user' },
    update: {},
    create: {
      name: '用户',
      code: 'user',
      description: '普通用户',
      isSystem: true,
    },
  })

  // 创建默认权限
  const permissions = [
    { code: 'users.read', name: '用户查看', module: 'users' },
    { code: 'users.create', name: '用户创建', module: 'users' },
    { code: 'users.update', name: '用户更新', module: 'users' },
    { code: 'users.delete', name: '用户删除', module: 'users' },
    { code: 'products.read', name: '产品查看', module: 'products' },
    { code: 'products.create', name: '产品创建', module: 'products' },
    { code: 'products.update', name: '产品更新', module: 'products' },
    { code: 'products.delete', name: '产品删除', module: 'products' },
    { code: 'inventory.read', name: '库存查看', module: 'inventory' },
    { code: 'inventory.create', name: '库存创建', module: 'inventory' },
    { code: 'inventory.update', name: '库存更新', module: 'inventory' },
    { code: 'inventory.delete', name: '库存删除', module: 'inventory' },
    { code: 'sales.read', name: '销售查看', module: 'sales' },
    { code: 'sales.create', name: '销售创建', module: 'sales' },
    { code: 'sales.update', name: '销售更新', module: 'sales' },
    { code: 'sales.delete', name: '销售删除', module: 'sales' },
    { code: 'finance.read', name: '财务查看', module: 'finance' },
    { code: 'finance.create', name: '财务创建', module: 'finance' },
    { code: 'finance.update', name: '财务更新', module: 'finance' },
    { code: 'finance.delete', name: '财务删除', module: 'finance' },
    { code: 'reports.read', name: '报表查看', module: 'reports' },
    { code: 'reports.export', name: '报表导出', module: 'reports' },
    { code: 'settings.read', name: '设置查看', module: 'settings' },
    { code: 'settings.update', name: '设置更新', module: 'settings' },
  ]

  for (const permission of permissions) {
    await prisma.permission.upsert({
      where: { code: permission.code },
      update: {},
      create: {
        name: permission.name,
        code: permission.code,
        module: permission.module,
        description: `权限: ${permission.name}`,
      },
    })
  }

  // 为管理员角色分配所有权限
  const allPermissions = await prisma.permission.findMany()
  for (const permission of allPermissions) {
    await prisma.rolePermission.upsert({
      where: {
        roleId_permissionId: {
          roleId: adminRole.id,
          permissionId: permission.id,
        },
      },
      update: {},
      create: {
        roleId: adminRole.id,
        permissionId: permission.id,
      },
    })
  }

  // 创建默认管理员用户
  const hashedPassword = await bcrypt.hash('admin123', 12)
  
  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: '系统管理员',
      password: hashedPassword,
      role: 'admin',
      emailVerified: new Date(),
    },
  })

  // 为管理员用户分配管理员角色
  await prisma.userRole.upsert({
    where: {
      userId_roleId: {
        userId: adminUser.id,
        roleId: adminRole.id,
      },
    },
    update: {},
    create: {
      userId: adminUser.id,
      roleId: adminRole.id,
    },
  })

  // 创建默认仓库
  const warehouseExists = await prisma.warehouse.findFirst({
    where: { name: '主仓库' }
  })
  
  if (!warehouseExists) {
    await prisma.warehouse.create({
      data: {
        name: '主仓库',
        type: 'main',
        location: '主要存储区域',
        isDefault: true,
      },
    })
  }

  // 创建默认产品分类
  const categoryExists = await prisma.productCategory.findFirst({
    where: { name: '景泰蓝' }
  })
  
  if (!categoryExists) {
    await prisma.productCategory.create({
      data: {
        name: '景泰蓝',
        description: '传统景泰蓝工艺品',
      },
    })
  }

  // 创建默认数据字典
  const statusDict = await prisma.dataDictionary.upsert({
    where: { code: 'STATUS' },
    update: {},
    create: {
      code: 'STATUS',
      name: '状态',
      description: '通用状态字典',
      isSystem: true,
    },
  })

  // 添加状态字典项
  const statusItems = [
    { code: 'ACTIVE', label: '激活', value: 'active' },
    { code: 'INACTIVE', label: '未激活', value: 'inactive' },
    { code: 'PENDING', label: '待处理', value: 'pending' },
    { code: 'COMPLETED', label: '已完成', value: 'completed' },
  ]

  for (const item of statusItems) {
    await prisma.dataDictionaryItem.upsert({
      where: {
        dictionaryId_code: {
          dictionaryId: statusDict.id,
          code: item.code,
        },
      },
      update: {},
      create: {
        dictionaryId: statusDict.id,
        code: item.code,
        label: item.label,
        value: item.value,
        isActive: true,
      },
    })
  }

  console.log('种子数据完成!')
  console.log('默认管理员账户: <EMAIL> / admin123')
}

main()
  .catch((e) => {
    console.error('种子数据失败:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })