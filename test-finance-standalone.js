/**
 * 财务模块独立测试脚本
 * 验证财务模块的核心功能和数据结构
 */

// 模拟财务账户数据
const sampleFinancialAccounts = [
  {
    id: 1,
    name: "工商银行基本账户",
    accountNumber: "6212261234567890123",
    accountType: "bank",
    bankName: "中国工商银行",
    initialBalance: 100000.00,
    currentBalance: 125600.50,
    isActive: true,
    notes: "公司主要账户",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-06-24")
  },
  {
    id: 2,
    name: "现金账户",
    accountNumber: null,
    accountType: "cash",
    bankName: null,
    initialBalance: 5000.00,
    currentBalance: 3200.00,
    isActive: true,
    notes: "日常现金收支",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-06-24")
  },
  {
    id: 3,
    name: "支付宝收款码",
    accountNumber: "payment_alipay_001",
    accountType: "alipay",
    bankName: null,
    initialBalance: 0.00,
    currentBalance: 12500.30,
    isActive: true,
    notes: "线上收款主要渠道",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-06-24")
  }
];

// 模拟财务分类数据
const sampleFinancialCategories = [
  {
    id: 1,
    name: "产品销售收入",
    type: "income",
    code: "INC_SALES",
    parentId: null,
    description: "珐琅产品销售收入",
    isSystem: true,
    isActive: true,
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01")
  },
  {
    id: 2,
    name: "工作坊收入",
    type: "income", 
    code: "INC_WORKSHOP",
    parentId: null,
    description: "工作坊教学收入",
    isSystem: true,
    isActive: true,
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01")
  },
  {
    id: 3,
    name: "材料采购支出",
    type: "expense",
    code: "EXP_MATERIAL",
    parentId: null,
    description: "原材料采购费用",
    isSystem: true,
    isActive: true,
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01")
  },
  {
    id: 4,
    name: "员工薪资支出", 
    type: "expense",
    code: "EXP_SALARY",
    parentId: null,
    description: "员工工资及提成",
    isSystem: true,
    isActive: true,
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01")
  }
];

// 模拟财务交易数据
const sampleFinancialTransactions = [
  {
    id: 1,
    transactionDate: new Date("2024-06-20"),
    amount: 2580.00,
    type: "income",
    accountId: 3, // 支付宝
    categoryId: 1, // 产品销售收入
    paymentMethod: "alipay",
    relatedId: 101,
    relatedType: "order",
    counterparty: "张女士",
    notes: "购买珐琅花瓶套装",
    attachmentUrl: null,
    status: "completed",
    createdById: "user_001",
    createdAt: new Date("2024-06-20"),
    updatedAt: new Date("2024-06-20")
  },
  {
    id: 2,
    transactionDate: new Date("2024-06-21"),
    amount: 1200.00,
    type: "income",
    accountId: 2, // 现金
    categoryId: 2, // 工作坊收入
    paymentMethod: "cash",
    relatedId: 201,
    relatedType: "workshop",
    counterparty: "李先生等4人",
    notes: "珐琅制作体验课程",
    attachmentUrl: null,
    status: "completed",
    createdById: "user_002",
    createdAt: new Date("2024-06-21"),
    updatedAt: new Date("2024-06-21")
  },
  {
    id: 3,
    transactionDate: new Date("2024-06-22"),
    amount: 3500.00,
    type: "expense",
    accountId: 1, // 银行
    categoryId: 3, // 材料采购
    paymentMethod: "bank_transfer",
    relatedId: 301,
    relatedType: "purchase_order",
    counterparty: "景泰蓝材料供应商",
    notes: "采购珐琅粉及铜丝",
    attachmentUrl: "/uploads/receipt_001.jpg",
    status: "completed",
    createdById: "user_001",
    createdAt: new Date("2024-06-22"),
    updatedAt: new Date("2024-06-22")
  }
];

// 财务统计函数
function calculateFinancialSummary(accounts, transactions) {
  const summary = {
    totalBalance: 0,
    totalIncome: 0,
    totalExpense: 0,
    netProfit: 0,
    profitMargin: 0,
    transactionCount: transactions.length,
    accountCount: accounts.filter(a => a.isActive).length
  };

  // 计算总余额
  summary.totalBalance = accounts
    .filter(account => account.isActive)
    .reduce((sum, account) => sum + account.currentBalance, 0);

  // 计算收入和支出
  transactions.forEach(transaction => {
    if (transaction.status === 'completed') {
      if (transaction.type === 'income') {
        summary.totalIncome += transaction.amount;
      } else if (transaction.type === 'expense') {
        summary.totalExpense += transaction.amount;
      }
    }
  });

  // 计算净利润和利润率
  summary.netProfit = summary.totalIncome - summary.totalExpense;
  summary.profitMargin = summary.totalIncome > 0 
    ? (summary.netProfit / summary.totalIncome) * 100 
    : 0;

  return summary;
}

// 按分类统计交易
function getTransactionsByCategory(transactions, categories) {
  const incomeByCategory = {};
  const expenseByCategory = {};

  categories.forEach(category => {
    if (category.type === 'income') {
      incomeByCategory[category.name] = 0;
    } else if (category.type === 'expense') {
      expenseByCategory[category.name] = 0;
    }
  });

  transactions.forEach(transaction => {
    if (transaction.status === 'completed' && transaction.categoryId) {
      const category = categories.find(c => c.id === transaction.categoryId);
      if (category) {
        if (category.type === 'income') {
          incomeByCategory[category.name] += transaction.amount;
        } else if (category.type === 'expense') {
          expenseByCategory[category.name] += transaction.amount;
        }
      }
    }
  });

  return { incomeByCategory, expenseByCategory };
}

// 验证账户数据结构
function validateAccountStructure(account) {
  const requiredFields = ['id', 'name', 'accountType', 'initialBalance', 'currentBalance', 'isActive'];
  const missingFields = requiredFields.filter(field => !(field in account));
  
  if (missingFields.length > 0) {
    return { valid: false, missing: missingFields };
  }
  
  return { valid: true, missing: [] };
}

// 验证交易数据结构
function validateTransactionStructure(transaction) {
  const requiredFields = ['id', 'transactionDate', 'amount', 'type', 'accountId', 'status'];
  const missingFields = requiredFields.filter(field => !(field in transaction));
  
  if (missingFields.length > 0) {
    return { valid: false, missing: missingFields };
  }
  
  return { valid: true, missing: [] };
}

// 执行测试
function runFinanceModuleTests() {
  console.log("🔧 财务模块独立测试开始...\n");

  // 1. 测试数据结构验证
  console.log("📊 1. 数据结构验证");
  const accountValidations = sampleFinancialAccounts.map(validateAccountStructure);
  const transactionValidations = sampleFinancialTransactions.map(validateTransactionStructure);
  
  console.log(`   账户数据结构: ${accountValidations.every(v => v.valid) ? '✅ 通过' : '❌ 失败'}`);
  console.log(`   交易数据结构: ${transactionValidations.every(v => v.valid) ? '✅ 通过' : '❌ 失败'}`);

  // 2. 测试财务统计
  console.log("\n💰 2. 财务统计计算");
  const summary = calculateFinancialSummary(sampleFinancialAccounts, sampleFinancialTransactions);
  console.log(`   总余额: ¥${summary.totalBalance.toFixed(2)}`);
  console.log(`   总收入: ¥${summary.totalIncome.toFixed(2)}`);
  console.log(`   总支出: ¥${summary.totalExpense.toFixed(2)}`);
  console.log(`   净利润: ¥${summary.netProfit.toFixed(2)}`);
  console.log(`   利润率: ${summary.profitMargin.toFixed(2)}%`);
  console.log(`   交易笔数: ${summary.transactionCount}`);
  console.log(`   活跃账户: ${summary.accountCount}`);

  // 3. 测试分类统计
  console.log("\n📋 3. 分类统计分析");
  const categoryStats = getTransactionsByCategory(sampleFinancialTransactions, sampleFinancialCategories);
  
  console.log("   收入分类:");
  Object.entries(categoryStats.incomeByCategory).forEach(([category, amount]) => {
    console.log(`     ${category}: ¥${amount.toFixed(2)}`);
  });
  
  console.log("   支出分类:");
  Object.entries(categoryStats.expenseByCategory).forEach(([category, amount]) => {
    console.log(`     ${category}: ¥${amount.toFixed(2)}`);
  });

  // 4. 测试账户类型分布
  console.log("\n🏦 4. 账户类型分析");
  const accountTypes = {};
  sampleFinancialAccounts.forEach(account => {
    if (!accountTypes[account.accountType]) {
      accountTypes[account.accountType] = { count: 0, balance: 0 };
    }
    accountTypes[account.accountType].count++;
    accountTypes[account.accountType].balance += account.currentBalance;
  });
  
  Object.entries(accountTypes).forEach(([type, data]) => {
    console.log(`   ${type}: ${data.count}个账户, 余额¥${data.balance.toFixed(2)}`);
  });

  // 5. 测试数据完整性
  console.log("\n🔍 5. 数据完整性检查");
  
  // 检查交易的账户ID是否存在
  const accountIds = sampleFinancialAccounts.map(a => a.id);
  const invalidAccountRefs = sampleFinancialTransactions.filter(t => !accountIds.includes(t.accountId));
  console.log(`   无效账户引用: ${invalidAccountRefs.length === 0 ? '✅ 无' : `❌ ${invalidAccountRefs.length}个`}`);
  
  // 检查交易的分类ID是否存在
  const categoryIds = sampleFinancialCategories.map(c => c.id);
  const invalidCategoryRefs = sampleFinancialTransactions.filter(t => 
    t.categoryId && !categoryIds.includes(t.categoryId)
  );
  console.log(`   无效分类引用: ${invalidCategoryRefs.length === 0 ? '✅ 无' : `❌ ${invalidCategoryRefs.length}个`}`);

  console.log("\n✅ 财务模块独立测试完成！");
  
  return {
    accountStructure: accountValidations.every(v => v.valid),
    transactionStructure: transactionValidations.every(v => v.valid),
    financialCalculation: summary.totalBalance > 0,
    categoryAnalysis: Object.keys(categoryStats.incomeByCategory).length > 0,
    dataIntegrity: invalidAccountRefs.length === 0 && invalidCategoryRefs.length === 0
  };
}

// 运行测试
const testResults = runFinanceModuleTests();

// 输出测试结果摘要
console.log("\n📝 测试结果摘要:");
console.log(`账户数据结构: ${testResults.accountStructure ? '✅' : '❌'}`);
console.log(`交易数据结构: ${testResults.transactionStructure ? '✅' : '❌'}`);
console.log(`财务计算功能: ${testResults.financialCalculation ? '✅' : '❌'}`);
console.log(`分类分析功能: ${testResults.categoryAnalysis ? '✅' : '❌'}`);
console.log(`数据完整性: ${testResults.dataIntegrity ? '✅' : '❌'}`);

const allPassed = Object.values(testResults).every(result => result === true);
console.log(`\n总体评估: ${allPassed ? '🎉 全部通过' : '⚠️  需要修复'}`);