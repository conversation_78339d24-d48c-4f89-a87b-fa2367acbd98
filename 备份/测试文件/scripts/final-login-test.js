const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function finalLoginTest() {
  try {
    console.log('=== 最终登录系统验证 ===\n');

    // 1. 验证数据库连接
    console.log('1. 验证数据库连接...');
    await prisma.$connect();
    console.log('✅ 数据库连接正常');

    // 2. 验证用户数据
    console.log('\n2. 验证用户数据...');
    const users = await prisma.user.findMany({
      include: {
        userRoles: {
          include: {
            role: true,
          },
        },
      },
    });

    console.log(`   找到 ${users.length} 个用户:`);
    users.forEach(user => {
      console.log(`   - ${user.name} (${user.email}) - 角色: ${user.role} - 分配角色: ${user.userRoles.map(ur => ur.role.name).join(', ')}`);
    });

    // 3. 验证超级管理员
    console.log('\n3. 验证超级管理员...');
    const adminUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
      include: {
        userRoles: {
          include: {
            role: {
              include: {
                rolePermissions: true,
              },
            },
          },
        },
      },
    });

    if (adminUser) {
      console.log('✅ 超级管理员账号存在');
      console.log(`   姓名: ${adminUser.name}`);
      console.log(`   邮箱: ${adminUser.email}`);
      console.log(`   密码: ${adminUser.password ? '已设置' : '未设置'}`);
      
      const totalPermissions = adminUser.userRoles.reduce((total, ur) => total + ur.role.rolePermissions.length, 0);
      console.log(`   权限数: ${totalPermissions}`);
    } else {
      console.log('❌ 超级管理员账号不存在');
    }

    // 4. 验证角色和权限
    console.log('\n4. 验证角色和权限...');
    const rolesCount = await prisma.role.count();
    const permissionsCount = await prisma.permission.count();
    console.log(`   角色数: ${rolesCount}`);
    console.log(`   权限数: ${permissionsCount}`);

    // 5. 检查最近的登录历史
    console.log('\n5. 检查最近的登录历史...');
    const recentLogins = await prisma.userLoginHistory.findMany({
      take: 5,
      orderBy: { loginTime: 'desc' },
      include: {
        user: {
          select: {
            name: true,
            email: true,
          },
        },
      },
    });

    if (recentLogins.length > 0) {
      console.log('   最近的登录记录:');
      recentLogins.forEach(login => {
        console.log(`   - ${login.user.name} (${login.user.email}) - ${login.loginTime} - ${login.status}`);
      });
    } else {
      console.log('   暂无登录记录');
    }

    // 6. 生成最终报告
    console.log('\n=== 最终报告 ===');
    console.log('✅ 用户登录和权限系统修复完成！');
    console.log('');
    console.log('🔧 已修复的问题:');
    console.log('   1. 重新创建了超级管理员账号 (<EMAIL>)');
    console.log('   2. 修复了用户角色权限分配');
    console.log('   3. 修复了NextAuth配置问题');
    console.log('   4. 修复了环境变量配置 (NEXTAUTH_URL)');
    console.log('   5. 添加了认证保护组件 (AuthGuard)');
    console.log('   6. 修复了登录重定向逻辑');
    console.log('');
    console.log('🎯 登录凭据:');
    console.log('   超级管理员:');
    console.log('   - 邮箱: <EMAIL>');
    console.log('   - 密码: Admin123456');
    console.log('');
    console.log('   经理账号:');
    console.log('   - 邮箱: <EMAIL>');
    console.log('   - 密码: Manager123456');
    console.log('');
    console.log('🌐 访问地址:');
    console.log('   - 登录页面: http://localhost:3001/login');
    console.log('   - 调试页面: http://localhost:3001/login-debug');
    console.log('   - 系统主页: http://localhost:3001/dashboard');
    console.log('');
    console.log('✨ 系统现在应该可以正常登录了！');

  } catch (error) {
    console.error('最终验证时发生错误:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 执行最终验证
finalLoginTest();
