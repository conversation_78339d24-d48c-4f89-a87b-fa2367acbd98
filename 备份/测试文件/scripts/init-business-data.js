#!/usr/bin/env node

/**
 * 初始化ERP系统业务数据脚本
 * 创建基础的用户、员工、产品、财务账户等测试数据
 */

const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function main() {
  console.log('🚀 开始初始化ERP业务数据...')

  try {
    // 1. 创建管理员用户
    console.log('📝 创建管理员用户...')
    const hashedPassword = await bcrypt.hash('admin123', 10)
    
    const adminUser = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        name: '系统管理员',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'admin',
      },
    })
    console.log('✅ 管理员用户创建成功:', adminUser.email)

    // 2. 创建基础角色
    console.log('🔐 创建基础角色...')
    const roles = [
      { name: '超级管理员', code: 'super_admin', description: '系统超级管理员，拥有所有权限' },
      { name: '管理员', code: 'admin', description: '系统管理员' },
      { name: '经理', code: 'manager', description: '部门经理' },
      { name: '员工', code: 'employee', description: '普通员工' },
      { name: '财务', code: 'finance', description: '财务人员' },
      { name: '库管', code: 'warehouse', description: '仓库管理员' },
    ]

    for (const role of roles) {
      await prisma.role.upsert({
        where: { code: role.code },
        update: {},
        create: role,
      })
    }
    console.log('✅ 基础角色创建成功')

    // 3. 创建基础权限
    console.log('🔑 创建基础权限...')
    const permissions = [
      { name: '查看产品', code: 'product.view', module: 'product' },
      { name: '创建产品', code: 'product.create', module: 'product' },
      { name: '编辑产品', code: 'product.edit', module: 'product' },
      { name: '删除产品', code: 'product.delete', module: 'product' },
      { name: '查看员工', code: 'employee.view', module: 'employee' },
      { name: '管理员工', code: 'employee.manage', module: 'employee' },
      { name: '查看财务', code: 'finance.view', module: 'finance' },
      { name: '管理财务', code: 'finance.manage', module: 'finance' },
      { name: '查看库存', code: 'inventory.view', module: 'inventory' },
      { name: '管理库存', code: 'inventory.manage', module: 'inventory' },
      { name: '系统管理', code: 'system.admin', module: 'system' },
    ]

    for (const permission of permissions) {
      await prisma.permission.upsert({
        where: { code: permission.code },
        update: {},
        create: permission,
      })
    }
    console.log('✅ 基础权限创建成功')

    // 4. 给管理员角色分配所有权限
    console.log('🔗 分配权限给管理员角色...')
    const adminRole = await prisma.role.findUnique({ where: { code: 'admin' } })
    const allPermissions = await prisma.permission.findMany()
    
    for (const permission of allPermissions) {
      await prisma.rolePermission.upsert({
        where: {
          roleId_permissionId: {
            roleId: adminRole.id,
            permissionId: permission.id,
          },
        },
        update: {},
        create: {
          roleId: adminRole.id,
          permissionId: permission.id,
        },
      })
    }
    console.log('✅ 管理员权限分配完成')

    // 5. 给管理员用户分配角色
    console.log('👤 分配角色给管理员用户...')
    await prisma.userRole.upsert({
      where: {
        userId_roleId: {
          userId: adminUser.id,
          roleId: adminRole.id,
        },
      },
      update: {},
      create: {
        userId: adminUser.id,
        roleId: adminRole.id,
      },
    })
    console.log('✅ 管理员用户角色分配完成')

    // 6. 创建测试员工
    console.log('👥 创建测试员工...')
    const employees = [
      { name: '张三', position: '设计师', phone: '13800138001', email: '<EMAIL>', dailySalary: 300 },
      { name: '李四', position: '生产主管', phone: '13800138002', email: '<EMAIL>', dailySalary: 350 },
      { name: '王五', position: '销售经理', phone: '13800138003', email: '<EMAIL>', dailySalary: 400 },
      { name: '赵六', position: '财务专员', phone: '13800138004', email: '<EMAIL>', dailySalary: 280 },
    ]

    for (const emp of employees) {
      // 检查是否已存在
      const existing = await prisma.employee.findFirst({
        where: { name: emp.name }
      })
      if (!existing) {
        await prisma.employee.create({
          data: emp,
        })
      }
    }
    console.log('✅ 测试员工创建成功')

    // 7. 创建产品分类
    console.log('📂 创建产品分类...')
    const categories = [
      { name: '景泰蓝工艺品', code: 'JTL001', description: '传统景泰蓝工艺制品' },
      { name: '装饰摆件', code: 'ZS001', description: '室内装饰摆设' },
      { name: '首饰配件', code: 'SS001', description: '珠宝首饰及配件' },
      { name: '艺术收藏', code: 'YS001', description: '艺术收藏品' },
    ]

    for (const category of categories) {
      await prisma.productCategory.upsert({
        where: { name: category.name },
        update: {},
        create: category,
      })
    }
    console.log('✅ 产品分类创建成功')

    // 8. 创建测试产品
    console.log('🎨 创建测试产品...')
    const category1 = await prisma.productCategory.findFirst({ where: { code: 'JTL001' } })
    const category2 = await prisma.productCategory.findFirst({ where: { code: 'ZS001' } })

    const products = [
      {
        name: '景泰蓝花瓶',
        price: 1200.00,
        commissionRate: 0.15,
        description: '传统手工景泰蓝花瓶，精美工艺',
        categoryId: category1.id,
        material: '铜胎',
        unit: '件',
        inventory: 50,
      },
      {
        name: '景泰蓝手镯',
        price: 680.00,
        commissionRate: 0.12,
        description: '精美景泰蓝手镯，送礼佳品',
        categoryId: category1.id,
        material: '铜胎',
        unit: '件',
        inventory: 30,
      },
      {
        name: '龙凤呈祥摆件',
        price: 2800.00,
        commissionRate: 0.18,
        description: '大型龙凤呈祥景泰蓝摆件',
        categoryId: category2.id,
        material: '铜胎',
        unit: '件',
        inventory: 15,
      },
    ]

    for (const product of products) {
      await prisma.product.upsert({
        where: { name: product.name },
        update: {},
        create: product,
      })
    }
    console.log('✅ 测试产品创建成功')

    // 9. 创建财务账户
    console.log('💰 创建财务账户...')
    const accounts = [
      {
        name: '现金账户',
        accountNumber: 'CASH001',
        accountType: 'cash',
        initialBalance: 50000.00,
        currentBalance: 50000.00,
      },
      {
        name: '银行基本户',
        accountNumber: '6222023602001234567',
        accountType: 'bank',
        bankName: '工商银行',
        initialBalance: 200000.00,
        currentBalance: 200000.00,
      },
      {
        name: '支付宝收款',
        accountNumber: '<EMAIL>',
        accountType: 'online',
        bankName: '支付宝',
        initialBalance: 10000.00,
        currentBalance: 10000.00,
      },
    ]

    for (const account of accounts) {
      await prisma.financialAccount.upsert({
        where: { name: account.name },
        update: {},
        create: account,
      })
    }
    console.log('✅ 财务账户创建成功')

    // 10. 创建仓库
    console.log('🏭 创建仓库...')
    const warehouses = [
      {
        name: '主仓库',
        code: 'WH001',
        location: '北京市朝阳区工厂路123号',
        description: '主要生产和存储仓库',
        isDefault: true,
      },
      {
        name: '展示仓库',
        code: 'WH002',
        location: '北京市朝阳区展示厅',
        description: '产品展示和临时存储',
        isDefault: false,
      },
    ]

    for (const warehouse of warehouses) {
      await prisma.warehouse.upsert({
        where: { code: warehouse.code },
        update: {},
        create: warehouse,
      })
    }
    console.log('✅ 仓库创建成功')

    // 11. 创建库存记录
    console.log('📦 创建库存记录...')
    const allProducts = await prisma.product.findMany()
    const mainWarehouse = await prisma.warehouse.findFirst({ where: { isDefault: true } })

    for (const product of allProducts) {
      await prisma.inventoryItem.upsert({
        where: {
          warehouseId_productId: {
            warehouseId: mainWarehouse.id,
            productId: product.id,
          },
        },
        update: {},
        create: {
          warehouseId: mainWarehouse.id,
          productId: product.id,
          quantity: product.inventory || 0,
          minQuantity: 10,
        },
      })
    }
    console.log('✅ 库存记录创建成功')

    // 12. 创建供应商
    console.log('🏢 创建供应商...')
    const suppliers = [
      {
        name: '北京铜胎供应商',
        contactPerson: '刘经理',
        phone: '010-12345678',
        email: '<EMAIL>',
        address: '北京市丰台区材料市场',
        supplierType: 'material',
      },
      {
        name: '景泰蓝颜料供应商',
        contactPerson: '陈经理',
        phone: '010-87654321',
        email: '<EMAIL>',
        address: '北京市海淀区化工园区',
        supplierType: 'material',
      },
    ]

    for (const supplier of suppliers) {
      await prisma.supplier.upsert({
        where: { name: supplier.name },
        update: {},
        create: supplier,
      })
    }
    console.log('✅ 供应商创建成功')

    // 13. 创建客户
    console.log('👤 创建客户...')
    const customers = [
      {
        name: '北京艺术品公司',
        phone: '010-11111111',
        email: '<EMAIL>',
        address: '北京市东城区艺术品街',
        type: 'corporate',
      },
      {
        name: '王女士',
        phone: '13900139001',
        email: '<EMAIL>',
        address: '北京市西城区XX小区',
        type: 'individual',
      },
    ]

    for (const customer of customers) {
      await prisma.customer.upsert({
        where: { name: customer.name },
        update: {},
        create: customer,
      })
    }
    console.log('✅ 客户创建成功')

    console.log('\n🎉 ERP业务数据初始化完成！')
    console.log('\n📊 数据统计:')
    console.log(`👤 用户: ${await prisma.user.count()}`)
    console.log(`👥 员工: ${await prisma.employee.count()}`)
    console.log(`🎨 产品: ${await prisma.product.count()}`)
    console.log(`📂 产品分类: ${await prisma.productCategory.count()}`)
    console.log(`💰 财务账户: ${await prisma.financialAccount.count()}`)
    console.log(`🏭 仓库: ${await prisma.warehouse.count()}`)
    console.log(`📦 库存项目: ${await prisma.inventoryItem.count()}`)
    console.log(`🏢 供应商: ${await prisma.supplier.count()}`)
    console.log(`👤 客户: ${await prisma.customer.count()}`)

    console.log('\n🔐 登录信息:')
    console.log('管理员账户: <EMAIL>')
    console.log('密码: admin123')

  } catch (error) {
    console.error('❌ 初始化数据时发生错误:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })