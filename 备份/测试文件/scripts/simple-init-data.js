#!/usr/bin/env node

/**
 * 简化版ERP系统业务数据初始化脚本
 */

const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function main() {
  console.log('🚀 开始初始化ERP基础数据...')

  try {
    // 1. 创建管理员用户
    console.log('📝 创建管理员用户...')
    const hashedPassword = await bcrypt.hash('admin123', 10)
    
    const existingUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    })

    let adminUser
    if (!existingUser) {
      adminUser = await prisma.user.create({
        data: {
          name: '系统管理员',
          email: '<EMAIL>',
          password: hashedPassword,
          role: 'admin',
        },
      })
      console.log('✅ 管理员用户创建成功:', adminUser.email)
    } else {
      adminUser = existingUser
      console.log('✅ 管理员用户已存在:', adminUser.email)
    }

    // 2. 创建产品分类
    console.log('📂 创建产品分类...')
    const categories = [
      { name: '景泰蓝工艺品', code: 'JTL001', description: '传统景泰蓝工艺制品' },
      { name: '装饰摆件', code: 'ZS001', description: '室内装饰摆设' },
    ]

    for (const category of categories) {
      const existing = await prisma.productCategory.findFirst({
        where: { name: category.name }
      })
      if (!existing) {
        await prisma.productCategory.create({ data: category })
      }
    }
    console.log('✅ 产品分类创建成功')

    // 3. 创建测试产品
    console.log('🎨 创建测试产品...')
    const category1 = await prisma.productCategory.findFirst({ where: { code: 'JTL001' } })

    const products = [
      {
        name: '景泰蓝花瓶',
        price: 1200.00,
        commissionRate: 0.15,
        description: '传统手工景泰蓝花瓶，精美工艺',
        categoryId: category1?.id,
        material: '铜胎',
        unit: '件',
        inventory: 50,
      },
      {
        name: '景泰蓝手镯',
        price: 680.00,
        commissionRate: 0.12,
        description: '精美景泰蓝手镯，送礼佳品',
        categoryId: category1?.id,
        material: '铜胎',
        unit: '件',
        inventory: 30,
      },
    ]

    for (const product of products) {
      const existing = await prisma.product.findFirst({
        where: { name: product.name }
      })
      if (!existing) {
        await prisma.product.create({ data: product })
      }
    }
    console.log('✅ 测试产品创建成功')

    // 4. 创建员工
    console.log('👥 创建测试员工...')
    const employees = [
      { name: '张三', position: '设计师', phone: '13800138001', email: '<EMAIL>', dailySalary: 300 },
      { name: '李四', position: '生产主管', phone: '***********', email: '<EMAIL>', dailySalary: 350 },
    ]

    for (const emp of employees) {
      const existing = await prisma.employee.findFirst({
        where: { name: emp.name }
      })
      if (!existing) {
        await prisma.employee.create({ data: emp })
      }
    }
    console.log('✅ 测试员工创建成功')

    // 5. 创建财务账户
    console.log('💰 创建财务账户...')
    const accounts = [
      {
        name: '现金账户',
        accountNumber: 'CASH001',
        accountType: 'cash',
        initialBalance: 50000.00,
        currentBalance: 50000.00,
      },
      {
        name: '银行基本户',
        accountNumber: '6222023602001234567',
        accountType: 'bank',
        bankName: '工商银行',
        initialBalance: 200000.00,
        currentBalance: 200000.00,
      },
    ]

    for (const account of accounts) {
      const existing = await prisma.financialAccount.findFirst({
        where: { name: account.name }
      })
      if (!existing) {
        await prisma.financialAccount.create({ data: account })
      }
    }
    console.log('✅ 财务账户创建成功')

    // 6. 创建仓库
    console.log('🏭 创建仓库...')
    const warehouses = [
      {
        name: '主仓库',
        code: 'WH001',
        location: '北京市朝阳区工厂路123号',
        description: '主要生产和存储仓库',
        isDefault: true,
      },
    ]

    for (const warehouse of warehouses) {
      const existing = await prisma.warehouse.findFirst({
        where: { code: warehouse.code }
      })
      if (!existing) {
        await prisma.warehouse.create({ data: warehouse })
      }
    }
    console.log('✅ 仓库创建成功')

    // 7. 创建库存记录
    console.log('📦 创建库存记录...')
    const allProducts = await prisma.product.findMany()
    const mainWarehouse = await prisma.warehouse.findFirst({ where: { isDefault: true } })

    if (mainWarehouse) {
      for (const product of allProducts) {
        const existing = await prisma.inventoryItem.findFirst({
          where: {
            warehouseId: mainWarehouse.id,
            productId: product.id,
          }
        })
        if (!existing) {
          await prisma.inventoryItem.create({
            data: {
              warehouseId: mainWarehouse.id,
              productId: product.id,
              quantity: product.inventory || 0,
              minQuantity: 10,
            }
          })
        }
      }
    }
    console.log('✅ 库存记录创建成功')

    console.log('\n🎉 ERP基础数据初始化完成！')
    console.log('\n📊 数据统计:')
    console.log(`👤 用户: ${await prisma.user.count()}`)
    console.log(`👥 员工: ${await prisma.employee.count()}`)
    console.log(`🎨 产品: ${await prisma.product.count()}`)
    console.log(`📂 产品分类: ${await prisma.productCategory.count()}`)
    console.log(`💰 财务账户: ${await prisma.financialAccount.count()}`)
    console.log(`🏭 仓库: ${await prisma.warehouse.count()}`)
    console.log(`📦 库存项目: ${await prisma.inventoryItem.count()}`)

    console.log('\n🔐 登录信息:')
    console.log('管理员账户: <EMAIL>')
    console.log('密码: admin123')

  } catch (error) {
    console.error('❌ 初始化数据时发生错误:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })