#!/bin/bash

# 聆花掐丝珐琅馆ERP系统 - 4GB内存服务器启动脚本

set -e

echo "🚀 启动聆花掐丝珐琅馆ERP系统 (4GB内存优化版)"
echo "================================================"

# 检查系统内存
TOTAL_MEM=$(free -m | awk 'NR==2{printf "%.0f", $2}')
echo "📊 系统总内存: ${TOTAL_MEM}MB"

if [ "$TOTAL_MEM" -lt 3500 ]; then
    echo "⚠️  警告: 系统内存少于3.5GB，可能影响性能"
fi

# 设置环境变量
export NODE_ENV=production
export NODE_OPTIONS="--max-old-space-size=1024 --expose-gc"
export UV_THREADPOOL_SIZE=4

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose未安装，请先安装Docker Compose"
    exit 1
fi

# 检查配置文件
echo "🔧 检查配置文件..."
if [ ! -f ".env" ]; then
    echo "📝 创建环境配置文件..."
    cp .env.example .env
    echo "⚠️  请编辑 .env 文件配置数据库密码等信息"
fi

# 检查优化配置文件
if [ ! -f "next.config.optimized.mjs" ]; then
    echo "❌ 缺少优化配置文件 next.config.optimized.mjs"
    exit 1
fi

if [ ! -f "postgresql-4gb.conf" ]; then
    echo "❌ 缺少PostgreSQL优化配置文件"
    exit 1
fi

if [ ! -f "nginx-4gb.conf" ]; then
    echo "❌ 缺少Nginx优化配置文件"
    exit 1
fi

# 停止现有容器
echo "🛑 停止现有容器..."
docker-compose -f docker-compose.4gb.yml down --remove-orphans

# 清理未使用的Docker资源
echo "🧹 清理Docker资源..."
docker system prune -f

# 构建镜像
echo "🔨 构建应用镜像..."
docker-compose -f docker-compose.4gb.yml build --no-cache

# 启动服务
echo "🚀 启动服务..."
docker-compose -f docker-compose.4gb.yml up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 30

# 检查服务状态
echo "📋 检查服务状态..."
docker-compose -f docker-compose.4gb.yml ps

# 检查健康状态
echo "🏥 检查应用健康状态..."
for i in {1..10}; do
    if curl -f http://localhost/health > /dev/null 2>&1; then
        echo "✅ 应用健康检查通过"
        break
    else
        echo "⏳ 等待应用启动... ($i/10)"
        sleep 10
    fi
    
    if [ $i -eq 10 ]; then
        echo "❌ 应用健康检查失败"
        echo "📋 查看应用日志:"
        docker-compose -f docker-compose.4gb.yml logs app
        exit 1
    fi
done

# 运行数据库迁移
echo "🗄️  运行数据库迁移..."
docker-compose -f docker-compose.4gb.yml exec app npx prisma db push

# 显示内存使用情况
echo "📊 当前内存使用情况:"
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}"

# 显示访问信息
echo ""
echo "🎉 系统启动完成!"
echo "================================================"
echo "🌐 Web访问地址: http://localhost"
echo "🔧 Prisma Studio: docker-compose -f docker-compose.4gb.yml exec app npx prisma studio"
echo "📊 监控命令: docker stats"
echo "📋 查看日志: docker-compose -f docker-compose.4gb.yml logs -f"
echo "🛑 停止系统: docker-compose -f docker-compose.4gb.yml down"
echo ""
echo "💡 内存优化提示:"
echo "   - 系统已配置为4GB内存服务器优化"
echo "   - PostgreSQL限制: 512MB"
echo "   - Redis限制: 128MB"
echo "   - 应用限制: 1.5GB"
echo "   - Nginx限制: 64MB"
echo "   - 预留系统内存: ~1.8GB"
echo ""
echo "📞 如需技术支持，请查看日志文件或联系管理员"
