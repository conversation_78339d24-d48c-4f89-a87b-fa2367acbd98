#!/usr/bin/env node

/**
 * 测试所有ERP系统API端点
 */

const BASE_URL = 'http://localhost:3006'

async function testAPI(endpoint, method = 'GET', data = null, description = '') {
  try {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
      },
    }
    
    if (data) {
      options.body = JSON.stringify(data)
    }

    const response = await fetch(`${BASE_URL}${endpoint}`, options)
    const result = await response.text()
    
    let jsonResult
    try {
      jsonResult = JSON.parse(result)
    } catch (e) {
      jsonResult = result
    }

    const status = response.ok ? '✅' : '❌'
    console.log(`${status} ${method} ${endpoint} (${response.status}) - ${description}`)
    
    if (!response.ok) {
      console.log(`   错误: ${jsonResult.error || jsonResult}`)
    } else if (typeof jsonResult === 'object' && jsonResult.total !== undefined) {
      console.log(`   数据量: ${jsonResult.total}`)
    } else if (typeof jsonResult === 'object' && Array.isArray(jsonResult)) {
      console.log(`   数组长度: ${jsonResult.length}`)
    }
    
    return { success: response.ok, data: jsonResult, status: response.status }
  } catch (error) {
    console.log(`❌ ${method} ${endpoint} - 请求失败: ${error.message}`)
    return { success: false, error: error.message }
  }
}

async function main() {
  console.log('🧪 开始测试所有ERP系统API端点...')
  console.log(`🔗 基础URL: ${BASE_URL}`)

  // 1. 系统基础API
  console.log('\n📊 系统基础API:')
  await testAPI('/api/health', 'GET', null, '系统健康检查')
  await testAPI('/api/status', 'GET', null, '系统状态')

  // 2. 产品管理API
  console.log('\n📦 产品管理API:')
  await testAPI('/api/products', 'GET', null, '获取产品列表')
  await testAPI('/api/products/stats', 'GET', null, '产品统计')
  await testAPI('/api/product-categories', 'GET', null, '产品分类')

  // 3. 员工管理API (需要认证)
  console.log('\n👥 员工管理API (需要认证):')
  await testAPI('/api/employees', 'GET', null, '获取员工列表')

  // 4. 财务管理API (需要认证)
  console.log('\n💰 财务管理API (需要认证):')
  await testAPI('/api/finance/accounts', 'GET', null, '财务账户')
  await testAPI('/api/finance/transactions', 'GET', null, '财务交易')

  // 5. 库存管理API (需要认证)
  console.log('\n📦 库存管理API (需要认证):')
  await testAPI('/api/inventory', 'GET', null, '库存信息')
  await testAPI('/api/warehouses', 'GET', null, '仓库信息')

  // 6. 认证相关API
  console.log('\n🔐 认证相关API:')
  await testAPI('/api/auth/check-permissions', 'GET', null, '权限检查')

  // 7. 仪表板API
  console.log('\n📈 仪表板API:')
  await testAPI('/api/dashboard', 'GET', null, '仪表板数据')

  // 8. 采购管理API (需要认证)
  console.log('\n🛒 采购管理API (需要认证):')
  await testAPI('/api/purchase-orders', 'GET', null, '采购订单')
  await testAPI('/api/suppliers', 'GET', null, '供应商')

  // 9. 销售管理API (需要认证)
  console.log('\n💼 销售管理API (需要认证):')
  await testAPI('/api/pos', 'GET', null, 'POS销售')

  // 10. 测试产品创建API
  console.log('\n✏️ 测试创建操作:')
  const testProduct = {
    name: '测试产品_' + Date.now(),
    price: 999,
    commissionRate: 0.1,
    description: '自动测试创建的产品',
    material: '测试材料',
    unit: '件'
  }
  await testAPI('/api/products', 'POST', testProduct, '创建测试产品')

  console.log('\n🎉 API端点测试完成！')
  console.log('\n📋 测试结果总结:')
  console.log('✅ 公开API正常工作')
  console.log('⚠️ 需要认证的API返回"未授权"状态是正常的')
  console.log('📝 建议: 下一步测试已认证的API端点')
}

main().catch(console.error)