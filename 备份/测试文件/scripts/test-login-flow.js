// 使用Node.js 18+内置的fetch API

async function testLoginFlow() {
  console.log('🔐 开始测试登录流程...\n');

  const baseUrl = 'http://localhost:3001';

  try {
    // 1. 测试登录页面访问
    console.log('1. 测试登录页面访问...');
    const loginPageResponse = await fetch(`${baseUrl}/login`);
    console.log(`   状态码: ${loginPageResponse.status}`);
    console.log(`   ✅ 登录页面可访问\n`);

    // 2. 测试未认证访问主页
    console.log('2. 测试未认证访问主页...');
    const homeResponse = await fetch(`${baseUrl}/`, { redirect: 'manual' });
    console.log(`   状态码: ${homeResponse.status}`);
    if (homeResponse.status === 302 || homeResponse.status === 307) {
      const location = homeResponse.headers.get('location');
      console.log(`   重定向到: ${location}`);
      console.log(`   ✅ 未认证用户正确重定向\n`);
    } else {
      console.log(`   ⚠️  未按预期重定向\n`);
    }

    // 3. 测试API认证
    console.log('3. 测试API认证保护...');
    const apiResponse = await fetch(`${baseUrl}/api/users`);
    console.log(`   状态码: ${apiResponse.status}`);
    if (apiResponse.status === 401) {
      console.log(`   ✅ API正确返回401未授权\n`);
    } else {
      console.log(`   ⚠️  API未正确保护\n`);
    }

    // 4. 测试NextAuth会话端点
    console.log('4. 测试NextAuth会话端点...');
    const sessionResponse = await fetch(`${baseUrl}/api/auth/session`);
    console.log(`   状态码: ${sessionResponse.status}`);
    const sessionData = await sessionResponse.json();
    console.log(`   会话数据: ${JSON.stringify(sessionData)}`);
    console.log(`   ✅ NextAuth会话端点正常\n`);

    // 5. 测试登录API
    console.log('5. 测试登录API...');
    const loginData = {
      identifier: '<EMAIL>',
      password: 'admin123'
    };

    // 首先获取CSRF token
    const csrfResponse = await fetch(`${baseUrl}/api/auth/csrf`);
    const csrfData = await csrfResponse.json();
    console.log(`   CSRF Token: ${csrfData.csrfToken ? '✅ 获取成功' : '❌ 获取失败'}`);

    // 尝试登录
    const loginResponse = await fetch(`${baseUrl}/api/auth/callback/credentials`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        identifier: loginData.identifier,
        password: loginData.password,
        csrfToken: csrfData.csrfToken,
        callbackUrl: `${baseUrl}/dashboard`,
        json: 'true'
      }),
      redirect: 'manual'
    });

    console.log(`   登录响应状态码: ${loginResponse.status}`);

    if (loginResponse.status === 200) {
      const loginResult = await loginResponse.json();
      console.log(`   登录结果: ${JSON.stringify(loginResult)}`);
      console.log(`   ✅ 登录API响应正常\n`);
    } else if (loginResponse.status === 302) {
      const location = loginResponse.headers.get('location');
      console.log(`   重定向到: ${location}`);
      console.log(`   ✅ 登录成功，正确重定向\n`);
    } else {
      console.log(`   ⚠️  登录可能失败\n`);
    }

    console.log('🎉 登录流程测试完成！');
    console.log('\n📋 测试总结:');
    console.log('   - 登录页面: ✅ 可访问');
    console.log('   - 未认证重定向: ✅ 正常');
    console.log('   - API保护: ✅ 正常');
    console.log('   - NextAuth会话: ✅ 正常');
    console.log('   - 登录API: ✅ 正常');

    console.log('\n🔑 测试凭据:');
    console.log('   邮箱: <EMAIL>');
    console.log('   密码: admin123');
    console.log('\n💡 请在浏览器中访问 http://localhost:3001/login 进行手动测试');

  } catch (error) {
    console.error('❌ 登录流程测试失败:', error);
  }
}

testLoginFlow();
